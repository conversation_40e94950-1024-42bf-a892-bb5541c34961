import React from "react";
import { Search, Calendar, Filter } from "lucide-react";
import useTranslations from "@/hooks/useTranslations";
import SelectInput from "@/components/forms/SelectInput";
import TextInput from "@/components/forms/TextInput";
import { formatDateForInput } from "@/helpers/formatDate";

const OrderFilters = ({ filters, updateFilter, applyFilters, clearFilters, loading }) => {
    const { translate } = useTranslations();

    const handleSearch = (e) => {
        e.preventDefault();
        applyFilters();
    };

    // Status options for filter
    const statusOptions = [
        { label: translate("common.all", "All"), value: "" },
        { label: translate("order.status.pending", "Pending"), value: "PENDING" },
        { label: translate("order.status.processing", "Processing"), value: "PROCESSING" },
        { label: translate("order.status.confirmed", "Confirmed"), value: "CONFIRMED" },
        { label: translate("order.status.canceled", "Canceled"), value: "CANCELED" },
        { label: translate("order.status.expired", "Expired"), value: "EXPIRED" },
    ];

    return (
        <div className="bg-white rounded-lg shadow p-4 mb-6">
            <div className="flex flex-col md:flex-row gap-4 mb-4">
                {/* Search Bar */}
                <div className="flex-1">
                    <form onSubmit={handleSearch} className="relative">
                        <TextInput
                            type="text"
                            value={filters.search}
                            onChange={(e) => updateFilter("search", e.target.value)}
                            placeholder={translate("common.search", "Search orders...")}
                            className="pr-10"
                        />
                        <button
                            type="submit"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-primary"
                        >
                            <Search size={18} />
                        </button>
                    </form>
                </div>

                {/* Status Filter */}
                <div className="w-full md:w-1/3">
                    <SelectInput
                        options={statusOptions}
                        value={statusOptions.find((opt) => opt.value === filters.status)}
                        onChange={(option) => updateFilter("status", option?.value || "")}
                        placeholder={translate("order.filter_by_status", "Filter by Status")}
                        menuPortalTarget={document.body}
                    />
                </div>
            </div>

            {/* Date Filter */}
            <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        {translate("order.date_from", "Date From")}
                    </label>
                    <div className="relative">
                        <input
                            type="date"
                            value={filters.date_from}
                            onChange={(e) => updateFilter("date_from", e.target.value)}
                            className="w-full px-4 py-2 pr-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                        />
                    </div>
                </div>
                <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        {translate("order.date_to", "Date To")}
                    </label>
                    <div className="relative">
                        <input
                            type="date"
                            value={filters.date_to}
                            onChange={(e) => updateFilter("date_to", e.target.value)}
                            className="w-full px-4 py-2 pr-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                        />
                    </div>
                </div>
            </div>

            {/* Filter Actions */}
            <div className="flex justify-end gap-6">
                <button
                    onClick={clearFilters}
                    className="btn btn-outline"
                    disabled={loading}
                >
                    {translate("common.clear", "Clear")}
                </button>
                <button
                    onClick={applyFilters}
                    className="btn btn-primary"
                    disabled={loading}
                >
                    <Filter size={16} className="mr-1" />
                    {translate("common.apply_filters", "Apply Filters")}
                </button>
            </div>
        </div>
    );
};

export default OrderFilters;


