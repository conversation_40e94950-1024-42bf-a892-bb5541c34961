<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_no' => $this->order_no,
            'total_price' => $this->total_price,
            'quantity' => $this->quantity,
            'status' => [
                'value' => $this->status->value,
                'label' => $this->status->getLabel(),
                'color' => $this->status->getBadgeColour(),
            ],
            'purchase_date' => $this->purchase_date,
            'created_at' => $this->created_at,
            'ticket' => [
                'id' => $this->whenLoaded('ticket', fn() => $this->ticket->id),
                'ticket_no' => $this->whenLoaded('ticket', fn() => $this->ticket->ticket_no),
                'event' => $this->whenLoaded('ticket', function () {
                    if ($this->ticket->event) {
                        return [
                            'id' => $this->ticket->event->id,
                            'name' => $this->ticket->event->translation?->name ?? '',
                            'slug' => $this->ticket->event->slug,
                        ];
                    }
                    return null;
                }),
            ],
            'attendees' => AttendeeResource::collection($this->whenLoaded('attendees')),
            'transactions' => OrderTransactionResource::collection($this->whenLoaded('transactions')),
        ];
    }
}
