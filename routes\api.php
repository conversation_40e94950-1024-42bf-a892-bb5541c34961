<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\ClubController;
use App\Http\Controllers\Api\V1\EventController;
use App\Http\Controllers\Api\V1\OrderController;
use App\Http\Controllers\Api\V1\LeagueController;
use App\Http\Controllers\Api\V1\TicketController;
use App\Http\Controllers\Api\V1\StadiumController;
use App\Http\Controllers\Api\V1\CheckoutController;
use App\Http\Controllers\Api\V1\TranslationsController;
use App\Http\Controllers\Api\V1\StripeWebHookController;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use App\Http\Controllers\Api\V1\TicketReservationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */

Route::middleware(['set-locale'])->prefix('v1')->group(function () {
    Route::prefix('translations')->group(function () {
        Route::get('/', [TranslationsController::class, 'index'])->name('api.translations.index');
    });
    Route::prefix('events')->group(function () {
        Route::post('/', [EventController::class, 'index'])->name('api.events.index');
        Route::get('/filters', [EventController::class, 'getFilters'])->name('api.events.filters');
        Route::get('/{slug}', [EventController::class, 'show'])->name('api.events.show');
    });

    Route::prefix('stadiums')->group(function () {
        Route::post('/', [StadiumController::class, 'index'])->name('api.stadiums.index');
        Route::get('/filters', [StadiumController::class, 'getFilters'])->name('api.stadiums.filters');
        Route::get('/{slug}', [StadiumController::class, 'show'])->name('api.stadiums.show');
    });

    Route::prefix('clubs')->group(function () {
        Route::post('/', [ClubController::class, 'index'])->name('api.clubs.index');
        Route::get('/filters', [ClubController::class, 'getFilters'])->name('api.clubs.filters');
        Route::get('/{slug}', [ClubController::class, 'show'])->name('api.clubs.show');
    });

    Route::prefix('leagues')->group(function () {
        Route::post('/', [LeagueController::class, 'index'])->name('api.leagues.index');
        Route::get('/filters', [LeagueController::class, 'getFilters'])->name('api.leagues.filters');
        Route::get('/{slug}', [LeagueController::class, 'show'])->name('api.leagues.show');
    });

    Route::prefix('tickets')->group(function () {
        Route::post('/', [TicketController::class, 'index'])->name('api.tickets.index');
    });

    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/user', [AuthController::class, 'user'])->name('api.user');
        Route::post('/auth/logout', [AuthController::class, 'logout'])->name('api.logout');

        Route::prefix('reservation')->group(function () {
            Route::post('/lock', [TicketReservationController::class, 'createPreliminaryReservation'])->name('api.reservations.create');
            Route::get('/detail/{reservationId}', [TicketReservationController::class, 'getPreliminaryReservation'])->name('api.reservations.detail');
            Route::get('/check-active/{user_id}', [TicketReservationController::class, 'checkActiveReservation'])->name('api.reservations.check-active');
            Route::delete('/', [TicketReservationController::class, 'cancelReservation'])->name('api.reservations.cancel');
        });

        Route::prefix('checkout')->group(function () {
            Route::post('/session', [CheckoutController::class, 'createSession'])->name('api.checkout.create');
            Route::post('/success', [CheckoutController::class, 'handleSuccess'])->name('api.checkout.success');
            Route::post('/cancel', [CheckoutController::class, 'handleCancel'])->name('api.checkout.cancel');
        });

        Route::prefix('orders')->group(function () {
            Route::get('/', [OrderController::class, 'index'])->name('api.orders.index');
        });
    });
});

Route::post('/auth/login', [AuthController::class, 'login'])->name('api.login');

Route::post('/checkout/webhook', [StripeWebHookController::class, 'handleCheckoutWebhook'])
    ->withoutMiddleware([VerifyCsrfToken::class])
    ->name('checkout.webhook');
