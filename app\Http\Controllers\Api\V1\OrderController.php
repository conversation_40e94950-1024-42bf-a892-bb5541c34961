<?php

namespace App\Http\Controllers\Api\V1;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\OrderFilterRequest;
use App\Services\OrderService;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    protected $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function index(OrderFilterRequest $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', config('services.ticketgol.items_per_page'));

        // Get filter parameters as DTO
        $filtersDTO = $request->toDTO();

        $orders = $this->orderService->getPaginatedOrdersForUser(Auth::id(), $perPage, $filtersDTO, $page);

        return ApiResponse::success($orders);
    }
}
