import { <PERSON>, <PERSON> } from "@inertiajs/react";
import { User, MapPin, ShoppingBag, CreditCard, Ticket, MessageSquare, Wallet, Lock } from "lucide-react";
import useTranslations from "@/hooks/useTranslations";

import AppLayout from "@/layouts/AppLayout";

function Dashboard(props) {
    const { auth } = props;
    const { translate } = useTranslations();

    const accountMenuItems = [
        { icon: <User className="w-5 h-5" />, title: "My Profile", route: route("profile.edit") },
        { icon: <ShoppingBag className="w-5 h-5" />, title: "My Orders", route: route('my-account.orders') },
        // { icon: <Ticket className="w-5 h-5" />, title: "My Added Tickets", route: "#added-tickets" },
        { icon: <Ticket className="w-5 h-5" />, title: "My Tickets", route: "#ticket-orders" },
        { icon: <MessageSquare className="w-5 h-5" />, title: "My Support Requests", route: "#support" },
        // { icon: <Wallet className="w-5 h-5" />, title: "Withdraw Funds", route: "#withdraw" },
    ];

    return (
        <>
            <Head title={`${translate('common.menu.my_account')} - ${auth.user.name}`} />
            <div className="py-12">
                <div className="mx-auto max-w-[80%]">
                    <div className="overflow-hidden bg-white shadow-sm sm:rounded-lg">
                        <div className="p-6 text-gray-900">
                            <h1 className="text-2xl font-bold mb-8">
                                Welcome Back, {auth.user.name}
                            </h1>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {accountMenuItems.map((item, index) => (
                                    <Link
                                        key={index}
                                        href={item.route}
                                        className="flex items-center p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                                    >
                                        <div className="mr-3 text-primary">
                                            {item.icon}
                                        </div>
                                        <span className="font-medium">{item.title}</span>
                                    </Link>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

Dashboard.layout = (page) => <AppLayout children={page} />;

export default Dashboard;

