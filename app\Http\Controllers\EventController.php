<?php

namespace App\Http\Controllers;

use App\Enums\EventCategoryType;
use App\Models\Event;
use App\Services\EventService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class EventController extends Controller
{
    /**
     * @var EventService The service for handling events-related operations.
     */
    protected $eventService;

    /**
     * EventController constructor.
     *
     * @param  EventService  $eventService  The service for handling events-related operations.
     */
    public function __construct(
        EventService $eventService,
    ) {
        $this->eventService = $eventService;
    }

    /**
     * Display the Events listings
     */
    public function index(Request $request): Response
    {
        return Inertia::render('Events/Index', [
            'categories' => EventCategoryType::getOptionsWithKeyValuePair(),
        ]);
    }

    /**
     * Display the Events listings
     */
    public function show(Event $event)
    {
        // return redirect(route('cms.show', ['slug' => $event->slug, 'model' => 'Event']), 301);

        return Inertia::render('Events/Show', [
            'slug' => $event->slug,
        ]);
    }
}

