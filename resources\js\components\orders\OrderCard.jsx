import React, { forwardRef } from "react";
import useTranslations from "@/hooks/useTranslations";
import { formatDate } from "@/helpers/formatDate";

const OrderCard = forwardRef(({ order }, ref) => {
    const { translate } = useTranslations();

    const getStatusBadgeClass = (color) => {
        switch (color) {
            case "success":
                return "bg-green-100 text-green-800";
            case "warning":
                return "bg-yellow-100 text-yellow-800";
            case "danger":
                return "bg-red-100 text-red-800";
            case "info":
                return "bg-blue-100 text-blue-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    // Default Unsplash image for events without images
    const defaultEventImage =
        "https://images.unsplash.com/photo-1489944440615-453fc2b6a9a9?auto=format&fit=crop&q=80&w=300&h=200";

    return (
        <div
            ref={ref}
            className="bg-white rounded-lg shadow overflow-hidden"
        >
            <div className="p-4 border-b flex justify-between items-center">
                <div>
                    <h2 className="font-semibold">
                        {translate("order.order_number", "Order")}{" "}
                        #{order.order_no}
                    </h2>
                    <p className="text-sm text-gray-600">
                        {translate("order.created_at", "Created")}:{" "}
                        {formatDate(order.created_at)}
                    </p>
                </div>
                <span
                    className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(order.status.color)}`}
                >
                    {order.status.label}
                </span>
            </div>

            <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                    {/* Event Image */}
                    <div className="w-full md:w-1/4 flex-shrink-0">
                        <img
                            src={defaultEventImage}
                            alt={order.ticket.event?.name || translate("common.event", "Event")}
                            className="w-full h-32 object-cover rounded-md"
                        />
                    </div>

                    {/* Ticket Details */}
                    <div className="w-full md:w-3/4">
                        <h3 className="font-medium mb-2">
                            {translate("order.ticket_details", "Ticket Details")}
                        </h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3">
                            <div>
                                <p className="text-sm text-gray-600">
                                    {translate("common.event", "Event")}:
                                </p>
                                <p className="text-sm font-semibold">
                                    {order.ticket.event?.name || translate("common.not_available", "N/A")}
                                </p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-600">
                                    {translate("order.ticket_number", "Ticket")}:
                                </p>
                                <p className="text-sm font-semibold">
                                    #{order.ticket.ticket_no}
                                </p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-600">
                                    {translate("order.quantity", "Quantity")}:
                                </p>
                                <p className="text-sm font-semibold">
                                    {order.quantity}
                                </p>
                            </div>

                            <div>
                                <p className="text-sm text-gray-600">
                                    {translate("order.purchase_date", "Purchase Date")}:
                                </p>
                                <p className="text-sm font-semibold">
                                    {formatDate(order.purchase_date)}
                                </p>
                            </div>

                            {order.ticket.stadium_sector && (
                                <div>
                                    <p className="text-sm text-gray-600">
                                        {translate("common.sector", "Sector")}:
                                    </p>
                                    <p className="text-sm font-semibold">
                                        {order.ticket.stadium_sector.name}
                                    </p>
                                </div>
                            )}

                            {order.description && (
                                <div className="md:col-span-2">
                                    <p className="text-sm text-gray-600">
                                        {translate("common.description", "Description")}:
                                    </p>
                                    <p className="text-sm">{order.description}</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex justify-between items-center pt-4 mt-4 border-t">
                    <span className="font-medium">
                        {translate("order.total", "Total")}:
                    </span>
                    <span className="font-bold text-lg">${order.total_price}</span>
                </div>
            </div>
        </div>
    );
});

export default OrderCard;
