<?php

namespace App\Repositories;

use App\Enums\OrderStatus;
use App\Models\Order;
use Illuminate\Support\Facades\Auth;

class OrderRepository extends BaseRepository
{
    public function __construct(Order $model)
    {
        $this->model = $model;
    }

    public function createOrderWithAttendees($checkoutDTO, $ticket)
    {
        $user = Auth::user();

        $order = $this->model->create([
            'buyer_id' => $user->id,
            'ticket_id' => $ticket->id,
            'quantity' => $checkoutDTO->quantity,
            'total_price' => $checkoutDTO->quantity * $ticket->price,
            'status' => OrderStatus::PENDING,
            'created_by' => $user->id,
            'purchase_date' => now(),
        ]);

        $order->attendees()->createMany($checkoutDTO->attendees);

        return $order;
    }

    public function cancelOrder(Order $order)
    {
        return $this->update($order->id, [
            'status' => OrderStatus::CANCELED,
        ]);
    }

    public function expireOrder(Order $order)
    {
        return $this->update($order->id, [
            'status' => OrderStatus::EXPIRED,
        ]);
    }

    public function completeOrder(Order $order)
    {
        return $this->update($order->id, [
            'status' => OrderStatus::CONFIRMED,
            'purchase_date' => now(),
        ]);
    }

    public function getOrdersForUser($userId, $perPage = 10, $filters = [], $page = 1)
    {
        $query = $this->model->select([
                'id',
                'order_no',
                'buyer_id',
                'ticket_id',
                'quantity',
                'total_price',
                'status',
                'purchase_date',
                'created_at'
            ])
            ->with([
                'ticket:id,ticket_no,event_id,sector_id',
                'ticket.event:id,slug',
                'ticket.event.translation:id,event_id,name',
                'ticket.sector:id',
                'ticket.sector.translation:id,stadium_sector_id,name',
                'attendees:id,order_id,name,email,gender,dob',
                'transactions:id,order_id,status,currency_code,total_amount,paid_at,created_at'
            ])
            ->where('buyer_id', $userId);

        // Apply search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function($q) use ($search) {
                $q->where('order_no', 'like', "%{$search}%")
                  ->orWhereHas('ticket', function($q) use ($search) {
                      $q->where('ticket_no', 'like', "%{$search}%")
                        ->orWhereHas('event.translation', function($q) use ($search) {
                            $q->where('name', 'like', "%{$search}%");
                        });
                  });
            });
        }

        // Apply status filter
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Apply date range filters
        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc')
                     ->paginate($perPage, ['*'], 'page', $page);
    }
}
