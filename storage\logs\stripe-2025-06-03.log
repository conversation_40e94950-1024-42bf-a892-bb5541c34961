[2025-06-03 07:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 07:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 07:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 07:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 07:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 07:58:01] local.INFO: Cron job executed {"time":"2025-06-03 07:58:01"} 
[2025-06-03 07:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 07:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 07:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 07:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 07:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 07:59:01] local.INFO: Cron job executed {"time":"2025-06-03 07:59:01"} 
[2025-06-03 08:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:00:01] local.INFO: Cron job executed {"time":"2025-06-03 08:00:01"} 
[2025-06-03 08:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:01:01] local.INFO: Cron job executed {"time":"2025-06-03 08:01:01"} 
[2025-06-03 08:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:02:01] local.INFO: Cron job executed {"time":"2025-06-03 08:02:01"} 
[2025-06-03 08:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:03:01] local.INFO: Cron job executed {"time":"2025-06-03 08:03:01"} 
[2025-06-03 08:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:04:01] local.INFO: Cron job executed {"time":"2025-06-03 08:04:01"} 
[2025-06-03 08:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:05:01] local.INFO: Cron job executed {"time":"2025-06-03 08:05:01"} 
[2025-06-03 08:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:06:01] local.INFO: Cron job executed {"time":"2025-06-03 08:06:01"} 
[2025-06-03 08:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:07:01] local.INFO: Cron job executed {"time":"2025-06-03 08:07:01"} 
[2025-06-03 08:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:08:01] local.INFO: Cron job executed {"time":"2025-06-03 08:08:01"} 
[2025-06-03 08:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:09:01] local.INFO: Cron job executed {"time":"2025-06-03 08:09:01"} 
[2025-06-03 08:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:10:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:10:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:10:01] local.INFO: Cron job executed {"time":"2025-06-03 08:10:01"} 
[2025-06-03 08:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:11:01] local.INFO: Cron job executed {"time":"2025-06-03 08:11:01"} 
[2025-06-03 08:12:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:12:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:12:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:12:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:12:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:12:01] local.INFO: Cron job executed {"time":"2025-06-03 08:12:01"} 
[2025-06-03 08:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:13:01] local.INFO: Cron job executed {"time":"2025-06-03 08:13:01"} 
[2025-06-03 08:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:14:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:14:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:14:01] local.INFO: Cron job executed {"time":"2025-06-03 08:14:01"} 
[2025-06-03 08:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:15:01] local.INFO: Cron job executed {"time":"2025-06-03 08:15:01"} 
[2025-06-03 08:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:16:01] local.INFO: Cron job executed {"time":"2025-06-03 08:16:01"} 
[2025-06-03 08:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:17:01] local.INFO: Cron job executed {"time":"2025-06-03 08:17:01"} 
[2025-06-03 08:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:18:01] local.INFO: Cron job executed {"time":"2025-06-03 08:18:01"} 
[2025-06-03 08:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:19:01] local.INFO: Cron job executed {"time":"2025-06-03 08:19:01"} 
[2025-06-03 08:20:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:20:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:20:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:20:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:20:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:20:01] local.INFO: Cron job executed {"time":"2025-06-03 08:20:01"} 
[2025-06-03 08:21:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:21:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:21:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:21:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:21:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:21:01] local.INFO: Cron job executed {"time":"2025-06-03 08:21:01"} 
[2025-06-03 08:22:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:22:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:22:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:22:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:22:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:22:01] local.INFO: Cron job executed {"time":"2025-06-03 08:22:01"} 
[2025-06-03 08:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:23:01] local.INFO: Cron job executed {"time":"2025-06-03 08:23:01"} 
[2025-06-03 08:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:24:01] local.INFO: Cron job executed {"time":"2025-06-03 08:24:01"} 
[2025-06-03 08:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:28:01] local.INFO: Cron job executed {"time":"2025-06-03 08:28:01"} 
[2025-06-03 08:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:29:01] local.INFO: Cron job executed {"time":"2025-06-03 08:29:01"} 
[2025-06-03 08:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:30:01] local.INFO: Cron job executed {"time":"2025-06-03 08:30:01"} 
[2025-06-03 08:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:31:01] local.INFO: Cron job executed {"time":"2025-06-03 08:31:01"} 
[2025-06-03 08:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:32:01] local.INFO: Cron job executed {"time":"2025-06-03 08:32:01"} 
[2025-06-03 08:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:33:01] local.INFO: Cron job executed {"time":"2025-06-03 08:33:01"} 
[2025-06-03 08:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:34:01] local.INFO: Cron job executed {"time":"2025-06-03 08:34:01"} 
[2025-06-03 08:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:35:01] local.INFO: Cron job executed {"time":"2025-06-03 08:35:01"} 
[2025-06-03 08:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:36:01] local.INFO: Cron job executed {"time":"2025-06-03 08:36:01"} 
[2025-06-03 08:37:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:37:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:37:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:37:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:37:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:37:01] local.INFO: Cron job executed {"time":"2025-06-03 08:37:01"} 
[2025-06-03 08:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:38:01] local.INFO: Cron job executed {"time":"2025-06-03 08:38:01"} 
[2025-06-03 08:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:39:01] local.INFO: Cron job executed {"time":"2025-06-03 08:39:01"} 
[2025-06-03 08:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:40:01] local.INFO: Cron job executed {"time":"2025-06-03 08:40:01"} 
[2025-06-03 08:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:41:01] local.INFO: Cron job executed {"time":"2025-06-03 08:41:01"} 
[2025-06-03 08:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":7,"ticket_id":4,"user_id":7,"quantity":"1","status":"active","expires_at":"2025-06-03T08:36:46.000000Z","created_at":"2025-06-03T08:21:46.000000Z","updated_at":"2025-06-03T08:21:46.000000Z"}]}} 
[2025-06-03 08:42:01] local.INFO: Updating expired records {"ids":[7]} 
[2025-06-03 08:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":7,"ticket_id":4,"user_id":7,"quantity":"1","status":"active","expires_at":"2025-06-03T08:36:46.000000Z","created_at":"2025-06-03T08:21:46.000000Z","updated_at":"2025-06-03T08:21:46.000000Z"}]}} 
[2025-06-03 08:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:42:01] local.INFO: Cron job executed {"time":"2025-06-03 08:42:01"} 
[2025-06-03 08:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:43:01] local.INFO: Cron job executed {"time":"2025-06-03 08:43:01"} 
[2025-06-03 08:44:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:44:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:44:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:44:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:44:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":3,"status":"pending","order_id":15,"ticket_reservation_id":7,"user_id":7,"session_id":"cs_test_b1chHZz8IArsGJfdPg13BgwcWmlgYGLQCfbvQ8GLQiq8vafoMh2SimzKBe","currency_code":"eur","payment_intent_id":null,"payment_method_id":null,"payment_method_type":null,"total_amount":null,"paid_at":null,"refunded_at":null,"card_brand":null,"card_last_four":null,"created_at":"2025-06-03T08:23:06.000000Z","updated_at":"2025-06-03T08:23:06.000000Z","order":{"id":15,"order_no":"TGO015","buyer_id":7,"ticket_id":4,"quantity":"1","total_price":"757.53","status":"pending","purchase_date":"2025-06-03","description":null,"created_by":7,"created_at":"2025-06-03T08:23:05.000000Z","updated_at":"2025-06-03T08:23:05.000000Z","deleted_at":null}}]}} 
[2025-06-03 08:44:02] local.INFO: Transaction & Order status updated to expired  
[2025-06-03 08:44:02] local.INFO: Cron job executed {"time":"2025-06-03 08:44:02"} 
[2025-06-03 08:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:45:01] local.INFO: Cron job executed {"time":"2025-06-03 08:45:01"} 
[2025-06-03 08:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:46:01] local.INFO: Cron job executed {"time":"2025-06-03 08:46:01"} 
[2025-06-03 08:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:47:01] local.INFO: Cron job executed {"time":"2025-06-03 08:47:01"} 
[2025-06-03 08:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:48:01] local.INFO: Cron job executed {"time":"2025-06-03 08:48:01"} 
[2025-06-03 08:49:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:49:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:49:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:49:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:49:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:49:01] local.INFO: Cron job executed {"time":"2025-06-03 08:49:01"} 
[2025-06-03 08:49:26] local.INFO: Charge succeeded {"charge":{"Stripe\\Charge":{"id":"ch_3RVqXARhkfMMoe7t0OBEB6aC","object":"charge","amount":36504,"amount_captured":36504,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":"IN","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"asdf","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1748940565,"currency":"eur","customer":"cus_SQhxH6RKluAU6z","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":52,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RVqXARhkfMMoe7t0GpkniJP","payment_method":"pm_1RVqX9RhkfMMoe7t1s3nglWC","payment_method_details":{"card":{"amount_authorized":36504,"authorization_code":"593319","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":"pass"},"country":"US","exp_month":2,"exp_year":2026,"extended_authorization":{"status":"disabled"},"fingerprint":"4qHsohpy0cF1sMdU","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"521137211511110","overcapture":{"maximum_amount_capturable":36504,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KJXu-sEGMgZ1ObKy8_A6LBYqqON1h9amVWvpXz845HLsEdmMTeIuz1VNJEXaNSQAc61raslqiE6-s7WF","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-06-03 08:49:26] local.INFO: Charge succeeded {"charge":{"Stripe\\Charge":{"id":"ch_3RVqXARhkfMMoe7t0OBEB6aC","object":"charge","amount":36504,"amount_captured":36504,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":null,"billing_details":{"address":{"city":null,"country":"IN","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"asdf","phone":null,"tax_id":null},"calculated_statement_descriptor":"Stripe","captured":true,"created":1748940565,"currency":"eur","customer":"cus_SQhxH6RKluAU6z","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":52,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RVqXARhkfMMoe7t0GpkniJP","payment_method":"pm_1RVqX9RhkfMMoe7t1s3nglWC","payment_method_details":{"card":{"amount_authorized":36504,"authorization_code":"593319","brand":"visa","checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":"pass"},"country":"US","exp_month":2,"exp_year":2026,"extended_authorization":{"status":"disabled"},"fingerprint":"4qHsohpy0cF1sMdU","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"521137211511110","overcapture":{"maximum_amount_capturable":36504,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUjltVWZSaGtmTU1vZTd0KJXu-sEGMgZ1ObKy8_A6LBYqqON1h9amVWvpXz845HLsEdmMTeIuz1VNJEXaNSQAc61raslqiE6-s7WF","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}}} 
[2025-06-03 08:49:28] local.INFO: Payment intent created {"payment_intent":{"Stripe\\PaymentIntent":{"id":"pi_3RVqXARhkfMMoe7t0GpkniJP","object":"payment_intent","amount":36504,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":0,"application":null,"application_fee_amount":null,"automatic_payment_methods":null,"canceled_at":null,"cancellation_reason":null,"capture_method":"automatic_async","client_secret":"pi_3RVqXARhkfMMoe7t0GpkniJP_secret_hbCqZQaXqEGuuivOeL8soyUTp","confirmation_method":"automatic","created":1748940564,"currency":"eur","customer":null,"description":null,"last_payment_error":null,"latest_charge":null,"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":null,"payment_method_configuration_details":null,"payment_method_options":{"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"requires_payment_method","transfer_data":null,"transfer_group":null}}} 
[2025-06-03 08:49:28] local.INFO: Payment intent created {"payment_intent":{"Stripe\\PaymentIntent":{"id":"pi_3RVqXARhkfMMoe7t0GpkniJP","object":"payment_intent","amount":36504,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":0,"application":null,"application_fee_amount":null,"automatic_payment_methods":null,"canceled_at":null,"cancellation_reason":null,"capture_method":"automatic_async","client_secret":"pi_3RVqXARhkfMMoe7t0GpkniJP_secret_hbCqZQaXqEGuuivOeL8soyUTp","confirmation_method":"automatic","created":1748940564,"currency":"eur","customer":null,"description":null,"last_payment_error":null,"latest_charge":null,"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":null,"payment_method_configuration_details":null,"payment_method_options":{"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"requires_payment_method","transfer_data":null,"transfer_group":null}}} 
[2025-06-03 08:49:34] local.INFO: Checkout session completed {"session_completed":{"Stripe\\Checkout\\Session":{"id":"cs_test_b1GHUnPqSHJJdep7J52ND6sk40e1TTuuNV3IIR1gh5W1yN1c6owdibexnm","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":36504,"amount_total":36504,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http://ticketgol.test/checkout/cancel?session_id={CHECKOUT_SESSION_ID}","client_reference_id":null,"client_secret":null,"collected_information":{"shipping_details":null},"consent":null,"consent_collection":null,"created":**********,"currency":"eur","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":{"message":"We will send you an email with your tickets also can view your tickets in your profile."},"terms_of_service_acceptance":null},"customer":"cus_SQhxH6RKluAU6z","customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"IN","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"asdf","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":"<EMAIL>","discounts":[],"expires_at":**********,"invoice":"in_1RVqXBRhkfMMoe7tAvooQU6v","invoice_creation":{"enabled":true,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":{"type":"self"},"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"user_id":"7","order_id":"17","temp_ticket_reservation_id":"8"},"mode":"payment","payment_intent":"pi_3RVqXARhkfMMoe7t0GpkniJP","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":{"allow_redisplay_filters":["always"],"payment_method_remove":"disabled","payment_method_save":null},"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http://ticketgol.test/checkout/success?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null}}} 
[2025-06-03 08:49:34] local.INFO: webhook time {"time":"2025-06-03 08:49:34"} 
[2025-06-03 08:49:34] local.INFO: Transaction status updated to completed  
[2025-06-03 08:49:34] local.INFO: Order status updated to completed  
[2025-06-03 08:49:34] local.INFO: Reservation status updated to completed {"ticket_reservation":{"App\\Models\\TicketReservation":{"id":8,"ticket_id":12,"user_id":7,"quantity":"1","status":"completed","expires_at":"2025-06-03T08:58:05.000000Z","created_at":"2025-06-03T08:43:05.000000Z","updated_at":"2025-06-03T08:49:34.000000Z"}}} 
[2025-06-03 08:49:34] local.INFO: reservedCounter is  {"coutner":{"Redis":[]}} 
[2025-06-03 08:49:34] local.INFO: Checkout session completed {"session_completed":{"Stripe\\Checkout\\Session":{"id":"cs_test_b1GHUnPqSHJJdep7J52ND6sk40e1TTuuNV3IIR1gh5W1yN1c6owdibexnm","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":36504,"amount_total":36504,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http://ticketgol.test/checkout/cancel?session_id={CHECKOUT_SESSION_ID}","client_reference_id":null,"client_secret":null,"collected_information":{"shipping_details":null},"consent":null,"consent_collection":null,"created":**********,"currency":"eur","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":{"message":"We will send you an email with your tickets also can view your tickets in your profile."},"terms_of_service_acceptance":null},"customer":"cus_SQhxH6RKluAU6z","customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"IN","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"asdf","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":"<EMAIL>","discounts":[],"expires_at":**********,"invoice":"in_1RVqXBRhkfMMoe7tAvooQU6v","invoice_creation":{"enabled":true,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":{"type":"self"},"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"user_id":"7","order_id":"17","temp_ticket_reservation_id":"8"},"mode":"payment","payment_intent":"pi_3RVqXARhkfMMoe7t0GpkniJP","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":{"allow_redisplay_filters":["always"],"payment_method_remove":"disabled","payment_method_save":null},"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http://ticketgol.test/checkout/success?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null}}} 
[2025-06-03 08:49:34] local.INFO: webhook time {"time":"2025-06-03 08:49:34"} 
[2025-06-03 08:49:34] local.INFO: Transaction status updated to completed  
[2025-06-03 08:49:34] local.INFO: Order status updated to completed  
[2025-06-03 08:49:34] local.INFO: Reservation status updated to completed {"ticket_reservation":{"App\\Models\\TicketReservation":{"id":8,"ticket_id":12,"user_id":7,"quantity":"1","status":"completed","expires_at":"2025-06-03T08:58:05.000000Z","created_at":"2025-06-03T08:43:05.000000Z","updated_at":"2025-06-03T08:49:34.000000Z"}}} 
[2025-06-03 08:49:34] local.INFO: reservedCounter is  {"coutner":{"Redis":[]}} 
[2025-06-03 08:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:50:01] local.INFO: Cron job executed {"time":"2025-06-03 08:50:01"} 
[2025-06-03 08:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:51:01] local.INFO: Cron job executed {"time":"2025-06-03 08:51:01"} 
[2025-06-03 08:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:52:01] local.INFO: Cron job executed {"time":"2025-06-03 08:52:01"} 
[2025-06-03 08:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:53:01] local.INFO: Cron job executed {"time":"2025-06-03 08:53:01"} 
[2025-06-03 08:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:54:01] local.INFO: Cron job executed {"time":"2025-06-03 08:54:01"} 
[2025-06-03 08:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:55:01] local.INFO: Cron job executed {"time":"2025-06-03 08:55:01"} 
[2025-06-03 08:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:56:01] local.INFO: Cron job executed {"time":"2025-06-03 08:56:01"} 
[2025-06-03 08:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:57:01] local.INFO: Cron job executed {"time":"2025-06-03 08:57:01"} 
[2025-06-03 08:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:58:01] local.INFO: Cron job executed {"time":"2025-06-03 08:58:01"} 
[2025-06-03 08:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 08:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 08:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 08:59:01] local.INFO: Cron job executed {"time":"2025-06-03 08:59:01"} 
[2025-06-03 09:00:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:00:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:00:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:00:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:00:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:00:01] local.INFO: Cron job executed {"time":"2025-06-03 09:00:01"} 
[2025-06-03 09:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:01:01] local.INFO: Cron job executed {"time":"2025-06-03 09:01:01"} 
[2025-06-03 09:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:02:01] local.INFO: Cron job executed {"time":"2025-06-03 09:02:01"} 
[2025-06-03 09:03:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:03:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:03:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:03:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:03:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:03:01] local.INFO: Cron job executed {"time":"2025-06-03 09:03:01"} 
[2025-06-03 09:04:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:04:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:04:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:04:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:04:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":4,"status":"pending","order_id":16,"ticket_reservation_id":8,"user_id":7,"session_id":"cs_test_b15hEZ83NHrgfKc5uFyJFv1G6LAfAhc1ZJ4LWSjSkp3vqyeM1L4sgI4erh","currency_code":"eur","payment_intent_id":null,"payment_method_id":null,"payment_method_type":null,"total_amount":null,"paid_at":null,"refunded_at":null,"card_brand":null,"card_last_four":null,"created_at":"2025-06-03T08:43:35.000000Z","updated_at":"2025-06-03T08:43:35.000000Z","order":{"id":16,"order_no":"TGO016","buyer_id":7,"ticket_id":12,"quantity":"1","total_price":"295.34","status":"pending","purchase_date":"2025-06-03","description":null,"created_by":7,"created_at":"2025-06-03T08:43:34.000000Z","updated_at":"2025-06-03T08:43:34.000000Z","deleted_at":null}}]}} 
[2025-06-03 09:04:01] local.INFO: Transaction & Order status updated to expired  
[2025-06-03 09:04:02] local.INFO: Cron job executed {"time":"2025-06-03 09:04:02"} 
[2025-06-03 09:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:05:01] local.INFO: Cron job executed {"time":"2025-06-03 09:05:01"} 
[2025-06-03 09:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:06:01] local.INFO: Cron job executed {"time":"2025-06-03 09:06:01"} 
[2025-06-03 09:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:07:01] local.INFO: Cron job executed {"time":"2025-06-03 09:07:01"} 
[2025-06-03 09:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:08:01] local.INFO: Cron job executed {"time":"2025-06-03 09:08:01"} 
[2025-06-03 09:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:09:01] local.INFO: Cron job executed {"time":"2025-06-03 09:09:01"} 
[2025-06-03 09:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:10:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:10:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:10:01] local.INFO: Cron job executed {"time":"2025-06-03 09:10:01"} 
[2025-06-03 09:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:11:01] local.INFO: Cron job executed {"time":"2025-06-03 09:11:01"} 
[2025-06-03 09:12:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:12:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:12:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:12:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:12:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:12:01] local.INFO: Cron job executed {"time":"2025-06-03 09:12:01"} 
[2025-06-03 09:13:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:13:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:13:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:13:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:13:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:13:01] local.INFO: Cron job executed {"time":"2025-06-03 09:13:01"} 
[2025-06-03 09:14:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:14:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:14:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:14:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:14:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:14:01] local.INFO: Cron job executed {"time":"2025-06-03 09:14:01"} 
[2025-06-03 09:15:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:15:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:15:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:15:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:15:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:15:01] local.INFO: Cron job executed {"time":"2025-06-03 09:15:01"} 
[2025-06-03 09:16:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:16:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:16:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:16:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:16:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:16:01] local.INFO: Cron job executed {"time":"2025-06-03 09:16:01"} 
[2025-06-03 09:17:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:17:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:17:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:17:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:17:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:17:01] local.INFO: Cron job executed {"time":"2025-06-03 09:17:01"} 
[2025-06-03 09:18:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:18:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:18:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:18:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:18:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:18:01] local.INFO: Cron job executed {"time":"2025-06-03 09:18:01"} 
[2025-06-03 09:19:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:19:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:19:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:19:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:19:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:19:01] local.INFO: Cron job executed {"time":"2025-06-03 09:19:01"} 
[2025-06-03 09:20:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:20:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:20:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:20:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:20:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:20:01] local.INFO: Cron job executed {"time":"2025-06-03 09:20:01"} 
[2025-06-03 09:21:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:21:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:21:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:21:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:21:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:21:01] local.INFO: Cron job executed {"time":"2025-06-03 09:21:01"} 
[2025-06-03 09:22:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:22:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:22:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:22:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:22:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:22:01] local.INFO: Cron job executed {"time":"2025-06-03 09:22:01"} 
[2025-06-03 09:23:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:23:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:23:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:23:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:23:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:23:01] local.INFO: Cron job executed {"time":"2025-06-03 09:23:01"} 
[2025-06-03 09:24:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:24:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:24:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:24:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:24:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:24:01] local.INFO: Cron job executed {"time":"2025-06-03 09:24:01"} 
[2025-06-03 09:25:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:25:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:25:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:25:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:25:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:25:01] local.INFO: Cron job executed {"time":"2025-06-03 09:25:01"} 
[2025-06-03 09:26:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:26:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:26:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:26:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:26:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:26:01] local.INFO: Cron job executed {"time":"2025-06-03 09:26:01"} 
[2025-06-03 09:27:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:27:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:27:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:27:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:27:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:27:01] local.INFO: Cron job executed {"time":"2025-06-03 09:27:01"} 
[2025-06-03 09:28:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:28:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:28:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:28:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:28:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:28:01] local.INFO: Cron job executed {"time":"2025-06-03 09:28:01"} 
[2025-06-03 09:29:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:29:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:29:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:29:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:29:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:29:01] local.INFO: Cron job executed {"time":"2025-06-03 09:29:01"} 
[2025-06-03 09:30:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:30:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:30:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:30:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:30:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:30:01] local.INFO: Cron job executed {"time":"2025-06-03 09:30:01"} 
[2025-06-03 09:31:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:31:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:31:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:31:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:31:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:31:01] local.INFO: Cron job executed {"time":"2025-06-03 09:31:01"} 
[2025-06-03 09:32:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:32:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:32:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:32:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:32:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:32:01] local.INFO: Cron job executed {"time":"2025-06-03 09:32:01"} 
[2025-06-03 09:33:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:33:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:33:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:33:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:33:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:33:01] local.INFO: Cron job executed {"time":"2025-06-03 09:33:01"} 
[2025-06-03 09:34:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:34:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:34:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:34:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:34:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:34:01] local.INFO: Cron job executed {"time":"2025-06-03 09:34:01"} 
[2025-06-03 09:35:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:35:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:35:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:35:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:35:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:35:01] local.INFO: Cron job executed {"time":"2025-06-03 09:35:01"} 
[2025-06-03 09:36:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:36:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:36:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:36:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:36:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:36:01] local.INFO: Cron job executed {"time":"2025-06-03 09:36:01"} 
[2025-06-03 09:37:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:37:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:37:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:37:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:37:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:37:01] local.INFO: Cron job executed {"time":"2025-06-03 09:37:01"} 
[2025-06-03 09:38:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:38:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:38:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:38:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:38:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:38:01] local.INFO: Cron job executed {"time":"2025-06-03 09:38:01"} 
[2025-06-03 09:39:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:39:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:39:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:39:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:39:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:39:01] local.INFO: Cron job executed {"time":"2025-06-03 09:39:01"} 
[2025-06-03 09:40:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:40:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:40:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:40:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:40:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:40:01] local.INFO: Cron job executed {"time":"2025-06-03 09:40:01"} 
[2025-06-03 09:41:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:41:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:41:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:41:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:41:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:41:01] local.INFO: Cron job executed {"time":"2025-06-03 09:41:01"} 
[2025-06-03 09:42:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:42:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:42:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:42:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:42:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:42:01] local.INFO: Cron job executed {"time":"2025-06-03 09:42:01"} 
[2025-06-03 09:43:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:43:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:43:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:43:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:43:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:43:01] local.INFO: Cron job executed {"time":"2025-06-03 09:43:01"} 
[2025-06-03 09:44:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:44:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:44:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:44:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:44:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:44:01] local.INFO: Cron job executed {"time":"2025-06-03 09:44:01"} 
[2025-06-03 09:45:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:45:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:45:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:45:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:45:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:45:01] local.INFO: Cron job executed {"time":"2025-06-03 09:45:01"} 
[2025-06-03 09:46:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:46:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:46:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:46:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:46:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:46:01] local.INFO: Cron job executed {"time":"2025-06-03 09:46:01"} 
[2025-06-03 09:47:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:47:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:47:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:47:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:47:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:47:01] local.INFO: Cron job executed {"time":"2025-06-03 09:47:01"} 
[2025-06-03 09:48:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:48:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:48:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:48:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:48:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:48:01] local.INFO: Cron job executed {"time":"2025-06-03 09:48:01"} 
[2025-06-03 09:49:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:49:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:49:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:49:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:49:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:49:01] local.INFO: Cron job executed {"time":"2025-06-03 09:49:01"} 
[2025-06-03 09:50:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:50:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:50:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:50:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:50:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:50:01] local.INFO: Cron job executed {"time":"2025-06-03 09:50:01"} 
[2025-06-03 09:51:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:51:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:51:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:51:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:51:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:51:01] local.INFO: Cron job executed {"time":"2025-06-03 09:51:01"} 
[2025-06-03 09:52:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:52:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:52:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:52:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:52:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:52:01] local.INFO: Cron job executed {"time":"2025-06-03 09:52:01"} 
[2025-06-03 09:53:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:53:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:53:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:53:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:53:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:53:01] local.INFO: Cron job executed {"time":"2025-06-03 09:53:01"} 
[2025-06-03 09:54:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:54:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:54:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:54:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:54:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:54:01] local.INFO: Cron job executed {"time":"2025-06-03 09:54:01"} 
[2025-06-03 09:55:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:55:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:55:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:55:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:55:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:55:01] local.INFO: Cron job executed {"time":"2025-06-03 09:55:01"} 
[2025-06-03 09:56:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:56:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:56:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:56:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:56:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:56:01] local.INFO: Cron job executed {"time":"2025-06-03 09:56:01"} 
[2025-06-03 09:57:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:57:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:57:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:57:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:57:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:57:01] local.INFO: Cron job executed {"time":"2025-06-03 09:57:01"} 
[2025-06-03 09:58:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:58:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:58:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:58:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:58:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:58:01] local.INFO: Cron job executed {"time":"2025-06-03 09:58:01"} 
[2025-06-03 09:59:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:59:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 09:59:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:59:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 09:59:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 09:59:01] local.INFO: Cron job executed {"time":"2025-06-03 09:59:01"} 
[2025-06-03 10:00:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:00:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:00:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:00:02] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:00:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:00:02] local.INFO: Cron job executed {"time":"2025-06-03 10:00:02"} 
[2025-06-03 10:01:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:01:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:01:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:01:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:01:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:01:01] local.INFO: Cron job executed {"time":"2025-06-03 10:01:01"} 
[2025-06-03 10:02:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:02:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:02:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:02:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:02:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:02:01] local.INFO: Cron job executed {"time":"2025-06-03 10:02:01"} 
[2025-06-03 10:03:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:03:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:03:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:03:02] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:03:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:03:02] local.INFO: Cron job executed {"time":"2025-06-03 10:03:02"} 
[2025-06-03 10:04:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:04:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:04:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:04:02] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:04:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:04:02] local.INFO: Cron job executed {"time":"2025-06-03 10:04:02"} 
[2025-06-03 10:05:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:05:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:05:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:05:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:05:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:05:01] local.INFO: Cron job executed {"time":"2025-06-03 10:05:01"} 
[2025-06-03 10:06:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:06:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:06:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:06:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:06:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:06:01] local.INFO: Cron job executed {"time":"2025-06-03 10:06:01"} 
[2025-06-03 10:07:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:07:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:07:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:07:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:07:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:07:01] local.INFO: Cron job executed {"time":"2025-06-03 10:07:01"} 
[2025-06-03 10:08:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:08:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:08:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:08:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:08:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:08:01] local.INFO: Cron job executed {"time":"2025-06-03 10:08:01"} 
[2025-06-03 10:09:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:09:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:09:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:09:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:09:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:09:01] local.INFO: Cron job executed {"time":"2025-06-03 10:09:01"} 
[2025-06-03 10:10:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:10:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:10:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:10:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:10:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:10:01] local.INFO: Cron job executed {"time":"2025-06-03 10:10:01"} 
[2025-06-03 10:11:01] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:11:01] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:11:01] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:11:01] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:11:01] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:11:01] local.INFO: Cron job executed {"time":"2025-06-03 10:11:01"} 
[2025-06-03 10:12:02] local.INFO: Expired reservations {"expired_reservations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:12:02] local.INFO: Updating expired records {"ids":[]} 
[2025-06-03 10:12:02] local.INFO: Expired records status updated to Expired {"ticket resevrations":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:12:02] local.INFO: Expired records status updated to Expired  
[2025-06-03 10:12:02] local.INFO: Expired transactions {"expired_transactions":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-06-03 10:12:02] local.INFO: Cron job executed {"time":"2025-06-03 10:12:02"} 
