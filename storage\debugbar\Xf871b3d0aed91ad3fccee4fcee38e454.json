{"__meta": {"id": "Xf871b3d0aed91ad3fccee4fcee38e454", "datetime": "2025-06-03 11:25:33", "utime": **********.487907, "method": "GET", "uri": "/api/v1/orders?search=&status=EXPIRED&date_from=&date_to=&page=1", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.322156, "end": **********.487929, "duration": 0.1657731533050537, "duration_str": "166ms", "measures": [{"label": "Booting", "start": **********.322156, "relative_start": 0, "end": **********.405532, "relative_end": **********.405532, "duration": 0.08337593078613281, "duration_str": "83.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.40555, "relative_start": 0.08339405059814453, "end": **********.487932, "relative_end": 2.86102294921875e-06, "duration": 0.0823819637298584, "duration_str": "82.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5807280, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/orders", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@index", "namespace": null, "where": [], "as": "api.orders.index", "prefix": "api/v1/orders", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=19\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:19-35</a>"}, "queries": {"nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02007, "accumulated_duration_str": "20.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa' limit 1", "type": "query", "params": [], "bindings": ["RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.42188, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 3.438}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.429238, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 3.438, "width_percent": 3.288}, {"sql": "select count(*) as aggregate from `orders` where `buyer_id` = 7 and `status` = 'EXPIRED' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7, "EXPIRED"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 17, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 23}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.435857, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 6.726, "width_percent": 4.933}, {"sql": "select `id`, `order_no`, `buyer_id`, `ticket_id`, `quantity`, `total_price`, `status`, `purchase_date`, `created_at` from `orders` where `buyer_id` = 7 and `status` = 'EXPIRED' and `orders`.`deleted_at` is null order by `created_at` desc limit 9 offset 0", "type": "query", "params": [], "bindings": [7, "EXPIRED"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 17, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 23}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.4396892, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 11.659, "width_percent": 4.086}, {"sql": "select `id`, `ticket_no`, `event_id`, `sector_id` from `tickets` where `tickets`.`id` in (4, 12) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 23}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.443274, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 15.745, "width_percent": 3.787}, {"sql": "select `id`, `slug` from `events` where `events`.`id` in (8) and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 27, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 32}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.44796, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 19.532, "width_percent": 3.488}, {"sql": "select `id`, `event_id`, `name` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (8)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 23}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 32}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.452644, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 23.019, "width_percent": 4.086}, {"sql": "select `id` from `stadium_sectors` where `stadium_sectors`.`id` in (138) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 27, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 23}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 32}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.4568229, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 27.105, "width_percent": 11.609}, {"sql": "select `id`, `stadium_sector_id`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (138)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 23}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 32}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.462497, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 38.714, "width_percent": 9.268}, {"sql": "select `id`, `order_id`, `name`, `email`, `gender`, `dob` from `attendees` where `attendees`.`order_id` in (15, 16)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 23}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.467241, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 47.982, "width_percent": 3.488}, {"sql": "select `id`, `order_id`, `status`, `currency_code`, `total_amount`, `paid_at`, `created_at` from `order_transactions` where `order_transactions`.`order_id` in (15, 16)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 23}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.4696681, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 51.47, "width_percent": 2.192}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiRXdkM2hoOEdVZ29pRDNJSHJ2WUR6UVd3aExta0JtR1BJMTVDVE5PVyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6OTA6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC90ZWxlc2NvcGUvdGVsZXNjb3BlLWFwaS9xdWVyaWVzLzlmMTBlOWIzLTIxNDEtNDQ4Zi1iMTk4LTg4MzNkMmYxZThmZiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiRXdkM2hoOEdVZ29pRDNJSHJ2WUR6UVd3aExta0JtR1BJMTVDVE5PVyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6OTA6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC90ZWxlc2NvcGUvdGVsZXNjb3BlLWFwaS9xdWVyaWVzLzlmMTBlOWIzLTIxNDEtNDQ4Zi1iMTk4LTg4MzNkMmYxZThmZiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.476627, "duration": 0.009300000000000001, "duration_str": "9.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 53.662, "width_percent": 46.338}]}, "models": {"data": {"App\\Models\\Order": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\Attendee": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FAttendee.php&line=1", "ajax": false, "filename": "Attendee.php", "line": "?"}}, "App\\Models\\OrderTransaction": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrderTransaction.php&line=1", "ajax": false, "filename": "OrderTransaction.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}}, "count": 13, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Ewd3hh8GUgoiD3IHrvYDzQWwhLmkBmGPI15CTNOW", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/telescope/telescope-api/queries/9f10e9b3-2141-448f-b198-8833d2f1e8ff\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f10e9f3-9a33-4b2a-9a85-c1c5126fdc51\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders", "status_code": "<pre class=sf-dump id=sf-dump-1855863932 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1855863932\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-844573425 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">EXPIRED</span>\"\n  \"<span class=sf-dump-key>date_from</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_to</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844573425\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2012575450 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2012575450\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-759664453 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6IjB0elJVMGpOaExOZTdFTFZTbEIybnc9PSIsInZhbHVlIjoiMXlMaXQ2ZWtReVhiUkpRSDFQM2F1ZGxzeGROY1J0RW4vZER5SndubkVudzdVN1Azd0V2TllKVk1LZTZEUjFVTC9kanA2dE5QN08wMEpnclpMUTVXcU9vL05HQlVUSXlLK3hQeTE0UHlNTVBTUVlDVVZoampmV05sNmlveERzanUiLCJtYWMiOiJhZTk1NmUwOThiN2FiZTBkNDk4ZmU1YjliMTc0ZDIyZDliMmJmOWMzYmYxN2MxOTUyNGE4YWE4ZGIzNTUwZmE0IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IjVieWFaQVdDY09PcGNQZmxDc2l2TEE9PSIsInZhbHVlIjoiK3NHcFBvOElDMThsaHcxQU1JZHJUR242T3pLaEpjaVU4Y2RVbEhPMnJIVzZIdW81cTZzSWlJbUUyakRtVDBFaWNIUmhuMUo1UFh0U3ZXc09jR2IrOWpyck1GNzc1bVh5WVZMN1NQak0vVkF5SkZqZ1VGRkdMazZIaUpzL1c1N1giLCJtYWMiOiJhYmI0ZDVkZDllZGVkMDdkZmFiMjFkYzBlNWQ3MTdjZTA1M2NjMGFhZWFmY2RmNTEyY2EzMjE5ODE5ZGI1MWVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjB0elJVMGpOaExOZTdFTFZTbEIybnc9PSIsInZhbHVlIjoiMXlMaXQ2ZWtReVhiUkpRSDFQM2F1ZGxzeGROY1J0RW4vZER5SndubkVudzdVN1Azd0V2TllKVk1LZTZEUjFVTC9kanA2dE5QN08wMEpnclpMUTVXcU9vL05HQlVUSXlLK3hQeTE0UHlNTVBTUVlDVVZoampmV05sNmlveERzanUiLCJtYWMiOiJhZTk1NmUwOThiN2FiZTBkNDk4ZmU1YjliMTc0ZDIyZDliMmJmOWMzYmYxN2MxOTUyNGE4YWE4ZGIzNTUwZmE0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759664453\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1446527972 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ewd3hh8GUgoiD3IHrvYDzQWwhLmkBmGPI15CTNOW</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1446527972\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1719012792 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 11:25:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImZjRnJ3dmY0aDB5cTNkc0pBVU9PWGc9PSIsInZhbHVlIjoibFJ2M0FUdFJPa0xjZFV4Q0VHQWVnMDBycEFQbTQrb1BjQUJVTVUxbi9DcDg5MjdKbXRIbVowN2l5TTZZME9vVzRNREdBM2g5TkppcUtHRi84S0JaZGpmME83NmwyWENnNDY1ZVo5MDZURlpsbXBRMU56azNQL21taWx2bmhJbHYiLCJtYWMiOiIxNTAwMGRjYzE3YTRmMjkxOGU1NDBhMmQ1YzViOTIyZDFkZTBiZDZhNDZkZjAzMWVkZGY2NmEyZDA1ZDdjZDQ0IiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 13:25:33 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6IkR4cFJhZCs1cnhuVHJGSzBMVlJZTmc9PSIsInZhbHVlIjoiMDA1ZkI0dHlEcDV2S1Mya2ZTcVU4RlkvbTlPR3lMNVRhY2NKZC9IbVdZVWtleTBKNG8wNVhTZ05FeUM1TnRvWDBOQXEwbUdCZGRpM3pBcWJIN0ZjMGFvMExaOWpTZk54dXJENlZoa3lEK0lCb3BHN0hBcWtKbmc5NjlXZ2ZKcGwiLCJtYWMiOiI2MTU2YzMwMjJlMWU5MDY4NTNjNGZiOWUyNjdhNGJlNjhkMTFmYmRmMzBjZDFmZWYyNzFiNTFlOGE2NWRkOTJhIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 13:25:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImZjRnJ3dmY0aDB5cTNkc0pBVU9PWGc9PSIsInZhbHVlIjoibFJ2M0FUdFJPa0xjZFV4Q0VHQWVnMDBycEFQbTQrb1BjQUJVTVUxbi9DcDg5MjdKbXRIbVowN2l5TTZZME9vVzRNREdBM2g5TkppcUtHRi84S0JaZGpmME83NmwyWENnNDY1ZVo5MDZURlpsbXBRMU56azNQL21taWx2bmhJbHYiLCJtYWMiOiIxNTAwMGRjYzE3YTRmMjkxOGU1NDBhMmQ1YzViOTIyZDFkZTBiZDZhNDZkZjAzMWVkZGY2NmEyZDA1ZDdjZDQ0IiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 13:25:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6IkR4cFJhZCs1cnhuVHJGSzBMVlJZTmc9PSIsInZhbHVlIjoiMDA1ZkI0dHlEcDV2S1Mya2ZTcVU4RlkvbTlPR3lMNVRhY2NKZC9IbVdZVWtleTBKNG8wNVhTZ05FeUM1TnRvWDBOQXEwbUdCZGRpM3pBcWJIN0ZjMGFvMExaOWpTZk54dXJENlZoa3lEK0lCb3BHN0hBcWtKbmc5NjlXZ2ZKcGwiLCJtYWMiOiI2MTU2YzMwMjJlMWU5MDY4NTNjNGZiOWUyNjdhNGJlNjhkMTFmYmRmMzBjZDFmZWYyNzFiNTFlOGE2NWRkOTJhIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 13:25:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719012792\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-886554549 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ewd3hh8GUgoiD3IHrvYDzQWwhLmkBmGPI15CTNOW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"90 characters\">http://ticketgol.test/telescope/telescope-api/queries/9f10e9b3-2141-448f-b198-8833d2f1e8ff</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-886554549\", {\"maxDepth\":0})</script>\n"}}