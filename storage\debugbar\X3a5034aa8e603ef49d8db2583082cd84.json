{"__meta": {"id": "X3a5034aa8e603ef49d8db2583082cd84", "datetime": "2025-06-03 10:45:52", "utime": **********.95075, "method": "GET", "uri": "/api/v1/orders", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.828096, "end": **********.950763, "duration": 0.12266707420349121, "duration_str": "123ms", "measures": [{"label": "Booting", "start": **********.828096, "relative_start": 0, "end": **********.901441, "relative_end": **********.901441, "duration": 0.07334518432617188, "duration_str": "73.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.901454, "relative_start": 0.07335805892944336, "end": **********.950765, "relative_end": 1.9073486328125e-06, "duration": 0.049310922622680664, "duration_str": "49.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5606344, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/orders", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@index", "namespace": null, "where": [], "as": "api.orders.index", "prefix": "api/v1/orders", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=19\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:19-25</a>"}, "queries": {"nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.014450000000000001, "accumulated_duration_str": "14.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa' limit 1", "type": "query", "params": [], "bindings": ["RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.907478, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 4.706}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.912008, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 4.706, "width_percent": 5.19}, {"sql": "select count(*) as aggregate from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 22}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.9170702, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:55", "source": {"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=55", "ajax": false, "filename": "OrderService.php", "line": "55"}, "connection": "ticketgol", "explain": null, "start_percent": 9.896, "width_percent": 5.675}, {"sql": "select `orders`.`id`, `orders`.`order_no`, `orders`.`ticket_id`, `orders`.`quantity`, `orders`.`total_price`, `orders`.`status`, `orders`.`purchase_date`, `orders`.`created_at` from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null order by `created_at` desc, `created_at` desc limit 9 offset 0", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 22}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.919379, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:55", "source": {"index": 16, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=55", "ajax": false, "filename": "OrderService.php", "line": "55"}, "connection": "ticketgol", "explain": null, "start_percent": 15.571, "width_percent": 4.775}, {"sql": "select `id`, `ticket_no`, `event_id`, `price` from `tickets` where `tickets`.`id` in (4, 12) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.921657, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:55", "source": {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=55", "ajax": false, "filename": "OrderService.php", "line": "55"}, "connection": "ticketgol", "explain": null, "start_percent": 20.346, "width_percent": 2.561}, {"sql": "select `id`, `slug`, `date`, `time` from `events` where `events`.`id` in (8) and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 22}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 29, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.924989, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:55", "source": {"index": 26, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=55", "ajax": false, "filename": "OrderService.php", "line": "55"}, "connection": "ticketgol", "explain": null, "start_percent": 22.907, "width_percent": 2.907}, {"sql": "select `event_id`, `name` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (8)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 22}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 34, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.929061, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:55", "source": {"index": 31, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=55", "ajax": false, "filename": "OrderService.php", "line": "55"}, "connection": "ticketgol", "explain": null, "start_percent": 25.813, "width_percent": 4.221}, {"sql": "select `id`, `order_id`, `name`, `email`, `gender`, `dob` from `attendees` where `attendees`.`order_id` in (15, 16, 17)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 22}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingRoutingDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php", "line": 35}], "start": **********.933255, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "OrderService.php:55", "source": {"index": 21, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FServices%2FOrderService.php&line=55", "ajax": false, "filename": "OrderService.php", "line": "55"}, "connection": "ticketgol", "explain": null, "start_percent": 30.035, "width_percent": 3.668}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiRXdkM2hoOEdVZ29pRDNJSHJ2WUR6UVd3aExta0JtR1BJMTVDVE5PVyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiRXdkM2hoOEdVZ29pRDNJSHJ2WUR6UVd3aExta0JtR1BJMTVDVE5PVyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.940109, "duration": 0.00958, "duration_str": "9.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 33.702, "width_percent": 66.298}]}, "models": {"data": {"App\\Models\\Order": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "App\\Models\\Attendee": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FAttendee.php&line=1", "ajax": false, "filename": "Attendee.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Ewd3hh8GUgoiD3IHrvYDzQWwhLmkBmGPI15CTNOW", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f10dbc3-3016-4ab7-a4c2-4251c7eab0ed\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1919126460 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6IjVxQ2kvMS81WDFSTXAycVkwVTVOVEE9PSIsInZhbHVlIjoiUW9COVd5K3VsN1RKbVRLVjNleUdyQ1JxZ09QQm15eW5xR1oxMWdTbC9waDF0bTYwNThUYkNuajV1QUc1Z1dsRnRDajV4cUl4QXNNRldIS0gxTTFlOTBIRnF2SWJFSFRieTRaUkVGQWIyTWU5MU51NFYvZUpQdnlhVExQc25qUkIiLCJtYWMiOiI1YTZmNGRjZjFjMWQ2YThlYjljZWI4Mzc2Njg5ODY5NzM5N2FiZjIzYjI2NmI0NDBhY2VhNTZhZjNkYTY2YTY3IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IlZHdDFzbTA2SlhUMWhSZUZJSW9VM1E9PSIsInZhbHVlIjoiclNkL0pFdjc3WisrbG1wS3NPdnE1K2dNc0JvbHJMaXRrMlVvVXhNeDVORmtmcE1uUVV0YU9HS3BGcTVWOEpPWW9BbEhNd3k0UHBJUmUxOUFDbW1IcnFlaHJ3emFmV2p4V0h6cHV1MEhaMEZWRjFmYkc3djYxb1djNHowT1loUm8iLCJtYWMiOiIzMzNlZDEyZmQyNzI3MDY2M2U1MzhlY2E3MmI4ZTZkZDQzM2Y0MWZiOGQ3N2M3YTY1NGFmYTQ5Y2ZlMTZmMDdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjVxQ2kvMS81WDFSTXAycVkwVTVOVEE9PSIsInZhbHVlIjoiUW9COVd5K3VsN1RKbVRLVjNleUdyQ1JxZ09QQm15eW5xR1oxMWdTbC9waDF0bTYwNThUYkNuajV1QUc1Z1dsRnRDajV4cUl4QXNNRldIS0gxTTFlOTBIRnF2SWJFSFRieTRaUkVGQWIyTWU5MU51NFYvZUpQdnlhVExQc25qUkIiLCJtYWMiOiI1YTZmNGRjZjFjMWQ2YThlYjljZWI4Mzc2Njg5ODY5NzM5N2FiZjIzYjI2NmI0NDBhY2VhNTZhZjNkYTY2YTY3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1919126460\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1791348456 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ewd3hh8GUgoiD3IHrvYDzQWwhLmkBmGPI15CTNOW</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1791348456\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1342818365 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 10:45:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImVFTVgzendaSHF6YzhGMDArNlh4bGc9PSIsInZhbHVlIjoibEQweDdzUnFZbG01KzBGM2ZjaEp4Q0VGNGxyV1kza0hFYlBSZmxOQjAybzRicmZXUlV5enRPWnpUT25ZYU9Ed2p0UksvZW5YbjZjbEl6UlVwSlA5TjdCR3FibE5lWnRpOVh2OEdSTllZZkNMcFRGVWlwK05uY0RaYjBoZm4xZmgiLCJtYWMiOiI2NjY1ODYzOTYzMmE1NDVlNTM5MGNhN2ZkZjkzNmJhMGQ4YWM1NWM3N2MyYzU2ZWRkYjY4NmRjNjRiZGExNTcyIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 12:45:52 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6Im9HdHcyb0c1YjF0TmxaN0lsblQraWc9PSIsInZhbHVlIjoiaFJnTmpMODAyMHlxUloyeUduc09XM0RaZDgwOW9iZzJpcHdia2FWQ3FwcW1Lelo4SXBjTlVJR05XK2pMdi83TmR3akFyWnRtYXJIY0R0VFlmUE9MZFBBVCtJdWluME1PY2ZveVBPZVR6WmxsdUM2UWlmR3ZWK28rOEd5UDRvYlMiLCJtYWMiOiJkMWUyMmQyMDQ1YTAwYTk3M2JhZmU3MmQ3MGU2YjU5YjQwZDJhMzAzMTgzYjY3NmYzZGQzOWU1YmU0YjJhODU1IiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 12:45:52 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImVFTVgzendaSHF6YzhGMDArNlh4bGc9PSIsInZhbHVlIjoibEQweDdzUnFZbG01KzBGM2ZjaEp4Q0VGNGxyV1kza0hFYlBSZmxOQjAybzRicmZXUlV5enRPWnpUT25ZYU9Ed2p0UksvZW5YbjZjbEl6UlVwSlA5TjdCR3FibE5lWnRpOVh2OEdSTllZZkNMcFRGVWlwK05uY0RaYjBoZm4xZmgiLCJtYWMiOiI2NjY1ODYzOTYzMmE1NDVlNTM5MGNhN2ZkZjkzNmJhMGQ4YWM1NWM3N2MyYzU2ZWRkYjY4NmRjNjRiZGExNTcyIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 12:45:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6Im9HdHcyb0c1YjF0TmxaN0lsblQraWc9PSIsInZhbHVlIjoiaFJnTmpMODAyMHlxUloyeUduc09XM0RaZDgwOW9iZzJpcHdia2FWQ3FwcW1Lelo4SXBjTlVJR05XK2pMdi83TmR3akFyWnRtYXJIY0R0VFlmUE9MZFBBVCtJdWluME1PY2ZveVBPZVR6WmxsdUM2UWlmR3ZWK28rOEd5UDRvYlMiLCJtYWMiOiJkMWUyMmQyMDQ1YTAwYTk3M2JhZmU3MmQ3MGU2YjU5YjQwZDJhMzAzMTgzYjY3NmYzZGQzOWU1YmU0YjJhODU1IiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 12:45:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342818365\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ewd3hh8GUgoiD3IHrvYDzQWwhLmkBmGPI15CTNOW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}