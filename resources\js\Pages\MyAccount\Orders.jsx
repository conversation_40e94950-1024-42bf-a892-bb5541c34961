import React, { useEffect } from "react";
import { Head } from "@inertiajs/react";
import useInfiniteScroll from "react-infinite-scroll-hook";
import useTranslations from "@/hooks/useTranslations";
import AppLayout from "@/layouts/AppLayout";
import useOrders from "@/hooks/useOrders";
import OrderCard from "@/components/orders/OrderCard";
import OrderFilters from "@/components/orders/OrderFilters";

function Orders() {
    const { translate } = useTranslations();

    const {
        orders,
        loading,
        error,
        hasNextPage,
        filters,
        updateFilter,
        clearFilters,
        fetchOrders,
        loadMoreOrders,
        applyFilters
    } = useOrders();

    // Fetch orders on component mount
    useEffect(() => {
        fetchOrders();
    }, []);

    // Setup infinite scroll hook
    const [infiniteRef] = useInfiniteScroll({
        loading,
        hasNextPage,
        onLoadMore: loadMoreOrders,
        rootMargin: "100px",
    });

    return (
        <>
            <Head title={translate("my_account.orders", "My Orders")} />

            <div className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-2xl font-bold mb-6">
                        {translate("my_account.orders", "My Orders")}
                    </h1>

                    {/* Search and Filters */}
                    <OrderFilters
                        filters={filters}
                        updateFilter={updateFilter}
                        applyFilters={applyFilters}
                        clearFilters={clearFilters}
                        loading={loading}
                    />

                    {/* Error message */}
                    {error && (
                        <div className="bg-red-100 text-red-700 p-4 rounded-lg mb-6">
                            {error}
                        </div>
                    )}

                    {/* Orders list */}
                    {orders.length === 0 && !loading ? (
                        <div className="bg-white rounded-lg shadow p-6 text-center">
                            <p>
                                {translate(
                                    "my_account.no_orders",
                                    "You don't have any orders yet."
                                )}
                            </p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {orders.map((order, index) => {
                                const isLastElement = index === orders.length - 1;
                                return (
                                    <OrderCard
                                        key={order.id}
                                        order={order}
                                        ref={isLastElement ? infiniteRef : null}
                                    />
                                );
                            })}

                            {/* Loading indicator */}
                            {loading && (
                                <div className="text-center py-4">
                                    <div
                                        className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
                                        role="status"
                                    >
                                        <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                                            {translate("common.loading", "Loading...")}
                                        </span>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}

Orders.layout = (page) => <AppLayout children={page} />;

export default Orders;
