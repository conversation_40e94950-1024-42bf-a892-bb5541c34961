import { useCallback, useState } from "react";
import axios from "axios";

export default function useOrders(initialOrders = { data: [], meta: {} }) {
    const [orders, setOrders] = useState(initialOrders.data || []);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [hasNextPage, setHasNextPage] = useState(
        initialOrders.meta?.current_page < initialOrders.meta?.last_page
    );
    const [page, setPage] = useState(initialOrders.meta?.current_page || 1);
    const [nextPageUrl, setNextPageUrl] = useState(initialOrders.meta?.next_page_url || null);

    // Search and filter states
    const [filters, setFilters] = useState({
        search: "",
        status: "",
        date_from: "",
        date_to: ""
    });

    const updateFilter = (key, value) => {
        setFilters(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const updateMultipleFilters = (filtersObject) => {
        setFilters(prev => ({
            ...prev,
            ...filtersObject
        }));
    };

    const clearFilters = useCallback(() => {
        setFilters({
            search: "",
            status: "",
            date_from: "",
            date_to: ""
        });
    }, []);

    const fetchOrders = async (url = route("api.orders.index"), appendData = false) => {
        setLoading(true);
        setError(null);

        try {
            const response = await axios.get(url, {
                params: {
                    ...filters,
                    page: appendData ? page + 1 : 1
                }
            });

            console.log(response); // Log the response to the console

            // Check if response and response.data exist
            if (response?.data?.success) {
                // Safely access nested properties
                const responseData = response.data.data || {};
                const newOrders = responseData.data || [];
                const meta = responseData.meta || {};

                if (appendData) {
                    // Create a map of existing orders by ID for quick lookup
                    const existingOrdersMap = new Map(
                        orders.map((order) => [order.id, order])
                    );

                    // Add only new orders that don't already exist
                    const uniqueNewOrders = newOrders.filter(
                        (order) => !existingOrdersMap.has(order.id)
                    );

                    setOrders(prev => [...prev, ...uniqueNewOrders]);
                    setPage(meta.current_page || page + 1);
                } else {
                    setOrders(newOrders);
                    setPage(1);
                }

                setHasNextPage(meta.current_page < meta.last_page);
                setNextPageUrl(meta.next_page_url);
            } else {
                throw new Error(response?.data?.message || "Failed to fetch orders");
            }
        } catch (err) {
            setError(err.message || "An error occurred while fetching orders");
            console.error("Error fetching orders:", err);
        } finally {
            setLoading(false);
        }
    };

    const loadMoreOrders = () => {
        if (!hasNextPage || loading || !nextPageUrl) return;
        fetchOrders(nextPageUrl, true);
    };

    const applyFilters = () => {
        fetchOrders();
    };

    return {
        orders,
        loading,
        error,
        hasNextPage,
        nextPageUrl,
        filters,
        updateFilter,
        updateMultipleFilters,
        clearFilters,
        fetchOrders,
        loadMoreOrders,
        applyFilters
    };
}
