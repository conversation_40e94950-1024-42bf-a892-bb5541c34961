<?php

namespace App\DTO;

use App\Contracts\DTOInterface;

class OrderFilterDTO implements DTOInterface
{
    public function __construct(
        public ?string $search = '',
        public ?string $status = '',
        public ?string $date_from = '',
        public ?string $date_to = '',
    ) {}

    public function toArray(): array
    {
        return [
            'search' => $this->search,
            'status' => $this->status,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
        ];
    }
}
