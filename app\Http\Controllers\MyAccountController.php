<?php

namespace App\Http\Controllers;

use App\DTO\OrderFilterDTO;
use App\Services\OrderService;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class MyAccountController extends Controller
{
    protected $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function index()
    {
        return Inertia::render('MyAccount/Index');
    }

    public function order()
    {
        return Inertia::render('MyAccount/Order');
    }

    public function orders()
    {
        $filtersDTO = new OrderFilterDTO();
        $initialOrders = $this->orderService->getPaginatedOrdersForUser(Auth::id(), 10, $filtersDTO);

        return Inertia::render('MyAccount/Orders', [
            'initialOrders' => $initialOrders
        ]);
    }
}
