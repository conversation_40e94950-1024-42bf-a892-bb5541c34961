{"__meta": {"id": "X976956aa775dddaf00f0f373929ab3f8", "datetime": "2025-06-03 10:32:11", "utime": **********.766479, "method": "GET", "uri": "/api/v1/events/filters", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.611395, "end": **********.766495, "duration": 0.15510010719299316, "duration_str": "155ms", "measures": [{"label": "Booting", "start": **********.611395, "relative_start": 0, "end": **********.672277, "relative_end": **********.672277, "duration": 0.0608820915222168, "duration_str": "60.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.672294, "relative_start": 0.06089901924133301, "end": **********.766497, "relative_end": 1.9073486328125e-06, "duration": 0.09420299530029297, "duration_str": "94.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6187872, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/events/filters", "middleware": "api, set-locale", "controller": "App\\Http\\Controllers\\Api\\V1\\EventController@getFilters", "namespace": null, "where": [], "as": "api.events.filters", "prefix": "api/v1/events", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FEventController.php&line=65\" onclick=\"\">app/Http/Controllers/Api/V1/EventController.php:65-86</a>"}, "queries": {"nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.015380000000000001, "accumulated_duration_str": "15.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa' limit 1", "type": "query", "params": [], "bindings": ["RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.678826, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 4.421}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.686529, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 4.421, "width_percent": 4.941}, {"sql": "select `id` from `stadiums` where `is_published` = 1 and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 35}, {"index": 16, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 68}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.696281, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "StadiumRepository.php:35", "source": {"index": 15, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FStadiumRepository.php&line=35", "ajax": false, "filename": "StadiumRepository.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 9.363, "width_percent": 4.551}, {"sql": "select `stadium_id`, `name` from `stadium_translations` where `locale` = 'en' and `stadium_translations`.`stadium_id` in (3, 4, 7, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 35}, {"index": 21, "namespace": null, "name": "app/Services/StadiumService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\StadiumService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 68}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.7031002, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "StadiumRepository.php:35", "source": {"index": 20, "namespace": null, "name": "app/Repositories/StadiumRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\StadiumRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FStadiumRepository.php&line=35", "ajax": false, "filename": "StadiumRepository.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 13.914, "width_percent": 5.267}, {"sql": "select `id` from `clubs` where `is_active` = 1 and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 35}, {"index": 16, "namespace": null, "name": "app/Services/ClubService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\ClubService.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 69}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.708152, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ClubRepository.php:35", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FClubRepository.php&line=35", "ajax": false, "filename": "ClubRepository.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 19.181, "width_percent": 4.616}, {"sql": "select `club_id`, `name` from `club_translations` where `locale` = 'en' and `club_translations`.`club_id` in (3, 5, 7, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 35}, {"index": 21, "namespace": null, "name": "app/Services/ClubService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\ClubService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 69}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.711987, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ClubRepository.php:35", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ClubRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\ClubRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FClubRepository.php&line=35", "ajax": false, "filename": "ClubRepository.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 23.797, "width_percent": 4.941}, {"sql": "select `id` from `leagues` where `is_published` = 1 and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 35}, {"index": 16, "namespace": null, "name": "app/Services/LeagueService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\LeagueService.php", "line": 40}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 70}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.716197, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "LeagueRepository.php:35", "source": {"index": 15, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FLeagueRepository.php&line=35", "ajax": false, "filename": "LeagueRepository.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 28.739, "width_percent": 4.161}, {"sql": "select `league_id`, `name` from `league_translations` where `locale` = 'en' and `league_translations`.`league_id` in (1, 3, 5, 6, 8, 9)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 35}, {"index": 21, "namespace": null, "name": "app/Services/LeagueService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\LeagueService.php", "line": 40}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 70}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.7203748, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "LeagueRepository.php:35", "source": {"index": 20, "namespace": null, "name": "app/Repositories/LeagueRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\LeagueRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FLeagueRepository.php&line=35", "ajax": false, "filename": "LeagueRepository.php", "line": "35"}, "connection": "ticketgol", "explain": null, "start_percent": 32.9, "width_percent": 5.007}, {"sql": "select `id` from `countries` where `is_published` = 1 and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Services/CountryService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\CountryService.php", "line": 49}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 71}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.724769, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "CountryRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FCountryRepository.php&line=29", "ajax": false, "filename": "CountryRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 37.906, "width_percent": 6.307}, {"sql": "select `country_id`, `name` from `country_translations` where `locale` = 'en' and `country_translations`.`country_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Services/CountryService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\CountryService.php", "line": 49}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/V1/EventController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\EventController.php", "line": 71}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.73843, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "CountryRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CountryRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\CountryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FCountryRepository.php&line=29", "ajax": false, "filename": "CountryRepository.php", "line": "29"}, "connection": "ticketgol", "explain": null, "start_percent": 44.213, "width_percent": 9.298}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiRXdkM2hoOEdVZ29pRDNJSHJ2WUR6UVd3aExta0JtR1BJMTVDVE5PVyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiRXdkM2hoOEdVZ29pRDNJSHJ2WUR6UVd3aExta0JtR1BJMTVDVE5PVyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly90aWNrZXRnb2wudGVzdC9teS1hY2NvdW50L29yZGVycyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjc7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.757879, "duration": 0.00715, "duration_str": "7.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 53.511, "width_percent": 46.489}]}, "models": {"data": {"App\\Models\\Country": {"value": 249, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\CountryTranslation": {"value": 249, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FCountryTranslation.php&line=1", "ajax": false, "filename": "CountryTranslation.php", "line": "?"}}, "App\\Models\\League": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeague.php&line=1", "ajax": false, "filename": "League.php", "line": "?"}}, "App\\Models\\LeagueTranslation": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FLeagueTranslation.php&line=1", "ajax": false, "filename": "LeagueTranslation.php", "line": "?"}}, "App\\Models\\Stadium": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadium.php&line=1", "ajax": false, "filename": "Stadium.php", "line": "?"}}, "App\\Models\\StadiumTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumTranslation.php&line=1", "ajax": false, "filename": "StadiumTranslation.php", "line": "?"}}, "App\\Models\\Club": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClub.php&line=1", "ajax": false, "filename": "Club.php", "line": "?"}}, "App\\Models\\ClubTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClubTranslation.php&line=1", "ajax": false, "filename": "ClubTranslation.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 531, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Ewd3hh8GUgoiD3IHrvYDzQWwhLmkBmGPI15CTNOW", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "password_hash_web": "$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f10d6de-29ba-4fbd-a94f-4f3a3fba7084\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/events/filters", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-376955672 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6IlRuZTg4UTZKbGtaYUkxN2NuSjkxcFE9PSIsInZhbHVlIjoib1Rja3VwZTB4d2doVXBqWFlOT3ZwSXpVd054cTdTay9ZckJEWkZiYURRTGxPeDR4VXpyQ0xnTmI3ZENtNjRNS2FRN2xSaDBQMzRnYnNNcUxkcCtOVWhGL1RBZTBPTFZCZW9WRUJscFF5cnM3OFpMZ0xYUnkzRmNBSnp3Q2Z6RW8iLCJtYWMiOiIyMWMzN2EwODRkODI3MzE5YmJiYzVkOGM3YzJmNzg1ZDU1ZDBlMWI5NjhiZjI2ZmYzOGZmMWM1ZmM4MWU3MDEzIiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IktLaHgwenVsWThOTTYraUp0MnpmNWc9PSIsInZhbHVlIjoidk1hS292djdpdEJvZHlHaWhJYWtqd3FmbG1FbStHeGg2Y1k1ZEFjU2tsMlZBcWlnS0ZydjRlSVpqMGxzdjBRcUdmMkRPWnp0eUpTQ1doZXpEdG10a2U3RmR0K2dSU25QdHBZNEkrSlFnZVRsWGdGNmpZbkZ1MURLb0dPaldIdEMiLCJtYWMiOiJhZTY4NmNkMGYyNmNiMzcxODMzMDcxMjVjYjQwZWQyMzE4MWQ0YmI5MDhhYTM2NzA2ZWJhODhmMGU0YjA1YjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://ticketgol.test/events</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlRuZTg4UTZKbGtaYUkxN2NuSjkxcFE9PSIsInZhbHVlIjoib1Rja3VwZTB4d2doVXBqWFlOT3ZwSXpVd054cTdTay9ZckJEWkZiYURRTGxPeDR4VXpyQ0xnTmI3ZENtNjRNS2FRN2xSaDBQMzRnYnNNcUxkcCtOVWhGL1RBZTBPTFZCZW9WRUJscFF5cnM3OFpMZ0xYUnkzRmNBSnp3Q2Z6RW8iLCJtYWMiOiIyMWMzN2EwODRkODI3MzE5YmJiYzVkOGM3YzJmNzg1ZDU1ZDBlMWI5NjhiZjI2ZmYzOGZmMWM1ZmM4MWU3MDEzIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376955672\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-662751290 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ewd3hh8GUgoiD3IHrvYDzQWwhLmkBmGPI15CTNOW</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RB2buOkIi7SgvBVU0Ctmvmw0FfC15SfSm05LPOAa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-662751290\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-142887217 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 10:32:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjVWRFI5ckJRZUpLK1dmZ1poR1U1Y3c9PSIsInZhbHVlIjoicVpBOVUrUlh3SDhEekdTb2k4dUhLOVROYUJDT3VtRmg4bmsxWlA3OUFwNDE4NE5aYThLZXV6WEpBdStFY1pzTTRwaUpxcnA0Qm01Sk51Wm4vYWU2TWJSUFM2dzNQZVV4Q1RVSVBpcnFta0plajVWMHB2ZjVKMFVCWXkveHFYaHUiLCJtYWMiOiI1ZjFhMmE1NDc4MjYyNTFiYWEwODgyYTg1ODJjZDM2YjE4ZjJlODJkNzMwMTQ4MmU0NThkMWJhY2U3ZGFjOGRkIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 12:32:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6InMyaVBnOEJvUktnUlhSNFhxSFpPL1E9PSIsInZhbHVlIjoiL1pTQ3p0eGpoeDRRYVVKNlZGdS94cG03Q3p2bERuK0IyNjJkK2cvY21sa1FyZ2NXVTI0LzZjOGxPcVpCTENxbHNPV3Q5dVYraWYwS1J6b2N6YmFMTnVGNUFNNWR1Y0xpMHdZb0pPN1lqNzlVRXczZ29EcUxoQVpuMXRsbXRSeGQiLCJtYWMiOiI4YzRmZjZjOTI4M2NhZTcxOGQ5ODljYTliMDAwZDg5ODhiMzI4ZDA2ZTdmNDY3NGUxZTk0NDViMDU5ZGQ1MTdiIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 12:32:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjVWRFI5ckJRZUpLK1dmZ1poR1U1Y3c9PSIsInZhbHVlIjoicVpBOVUrUlh3SDhEekdTb2k4dUhLOVROYUJDT3VtRmg4bmsxWlA3OUFwNDE4NE5aYThLZXV6WEpBdStFY1pzTTRwaUpxcnA0Qm01Sk51Wm4vYWU2TWJSUFM2dzNQZVV4Q1RVSVBpcnFta0plajVWMHB2ZjVKMFVCWXkveHFYaHUiLCJtYWMiOiI1ZjFhMmE1NDc4MjYyNTFiYWEwODgyYTg1ODJjZDM2YjE4ZjJlODJkNzMwMTQ4MmU0NThkMWJhY2U3ZGFjOGRkIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 12:32:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6InMyaVBnOEJvUktnUlhSNFhxSFpPL1E9PSIsInZhbHVlIjoiL1pTQ3p0eGpoeDRRYVVKNlZGdS94cG03Q3p2bERuK0IyNjJkK2cvY21sa1FyZ2NXVTI0LzZjOGxPcVpCTENxbHNPV3Q5dVYraWYwS1J6b2N6YmFMTnVGNUFNNWR1Y0xpMHdZb0pPN1lqNzlVRXczZ29EcUxoQVpuMXRsbXRSeGQiLCJtYWMiOiI4YzRmZjZjOTI4M2NhZTcxOGQ5ODljYTliMDAwZDg5ODhiMzI4ZDA2ZTdmNDY3NGUxZTk0NDViMDU5ZGQ1MTdiIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 12:32:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142887217\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ewd3hh8GUgoiD3IHrvYDzQWwhLmkBmGPI15CTNOW</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n"}}