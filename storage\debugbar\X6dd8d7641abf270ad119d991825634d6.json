{"__meta": {"id": "X6dd8d7641abf270ad119d991825634d6", "datetime": "2025-06-03 10:15:28", "utime": 1748945728.829073, "method": "GET", "uri": "/clubs", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.418045, "end": 1748945728.829088, "duration": 1.4110429286956787, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": **********.418045, "relative_start": 0, "end": **********.48943, "relative_end": **********.48943, "duration": 0.07138490676879883, "duration_str": "71.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.489627, "relative_start": 0.07158184051513672, "end": 1748945728.82909, "relative_end": 2.1457672119140625e-06, "duration": 1.339463233947754, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 14215448, "peak_usage_str": "14MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 35, "templates": [{"name": "__components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.576467, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}}, {"name": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": **********.585928, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.601589, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.62363, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": **********.633346, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.641958, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.648222, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": **********.654957, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.788741, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.811503, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.922206, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": **********.953572, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.016274, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.03123, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.095102, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.105334, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.151769, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.161799, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.213401, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.224348, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.271629, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.280256, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.329561, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.340132, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.38876, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.398737, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.442583, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "__components::4e08262e37252af4d0ec53b8f597c6de", "param_count": null, "params": [], "start": 1748945728.452467, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4e08262e37252af4d0ec53b8f597c6de.blade.php__components::4e08262e37252af4d0ec53b8f597c6de", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4e08262e37252af4d0ec53b8f597c6de.blade.php&line=1", "ajax": false, "filename": "4e08262e37252af4d0ec53b8f597c6de.blade.php", "line": "?"}}, {"name": "livewire::tailwind", "param_count": null, "params": [], "start": 1748945728.458731, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": 1748945728.474787, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1748945728.493105, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "__components::f07f1a332c895be3dafc362336ba959c", "param_count": null, "params": [], "start": 1748945728.60547, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/f07f1a332c895be3dafc362336ba959c.blade.php__components::f07f1a332c895be3dafc362336ba959c", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2Ff07f1a332c895be3dafc362336ba959c.blade.php&line=1", "ajax": false, "filename": "f07f1a332c895be3dafc362336ba959c.blade.php", "line": "?"}}, {"name": "filament-language-switch::language-switch", "param_count": null, "params": [], "start": 1748945728.60775, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/language-switch.blade.phpfilament-language-switch::language-switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Flanguage-switch.blade.php&line=1", "ajax": false, "filename": "language-switch.blade.php", "line": "?"}}, {"name": "filament-language-switch::switch", "param_count": null, "params": [], "start": 1748945728.60875, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\vendor\\bezhansalleh\\filament-language-switch\\src\\/../resources/views/switch.blade.phpfilament-language-switch::switch", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fbezhansalleh%2Ffilament-language-switch%2Fresources%2Fviews%2Fswitch.blade.php&line=1", "ajax": false, "filename": "switch.blade.php", "line": "?"}}, {"name": "__components::69d93d5cde0cc1ee5603a3b96a184e40", "param_count": null, "params": [], "start": 1748945728.815155, "type": "blade", "hash": "bladeE:\\Ticketgol\\Code\\Ticketgol\\storage\\framework\\views/69d93d5cde0cc1ee5603a3b96a184e40.blade.php__components::69d93d5cde0cc1ee5603a3b96a184e40", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fstorage%2Fframework%2Fviews%2F69d93d5cde0cc1ee5603a3b96a184e40.blade.php&line=1", "ajax": false, "filename": "69d93d5cde0cc1ee5603a3b96a184e40.blade.php", "line": "?"}}]}, "route": {"uri": "GET clubs", "domain": "admin.ticketgol.test", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Illuminate\\Session\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, BezhanSalleh\\FilamentLanguageSwitch\\Http\\Middleware\\SwitchLanguageLocale, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "excluded_middleware": [], "controller": "App\\Filament\\Resources\\ClubResource\\Pages\\ManageClubs@__invoke", "as": "filament.admin.resources.clubs.index", "namespace": null, "where": [], "prefix": "clubs", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPageComponents%2FHandlesPageComponents.php&line=7\" onclick=\"\">vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php:7-31</a>"}, "queries": {"nb_statements": 38, "nb_visible_statements": 38, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03535, "accumulated_duration_str": "35.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'UUSnUxEMdMwVKgxj28zxHDJH8p3IjljVcNv0YgJp' limit 1", "type": "query", "params": [], "bindings": ["UUSnUxEMdMwVKgxj28zxHDJH8p3IjljVcNv0YgJp"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.49604, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 1.443}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4996421, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 1.443, "width_percent": 2.32}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.507349, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "ticketgol", "explain": null, "start_percent": 3.762, "width_percent": 1.499}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.513617, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "ticketgol", "explain": null, "start_percent": 5.262, "width_percent": 2.093}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.517303, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "ticketgol", "explain": null, "start_percent": 7.355, "width_percent": 2.291}, {"sql": "select count(*) as aggregate from `clubs` left join `club_translations` on `clubs`.`id` = `club_translations`.`club_id` left join `stadium_translations` on `clubs`.`stadium_id` = `stadium_translations`.`stadium_id` left join `country_translations` on `clubs`.`country_id` = `country_translations`.`country_id` where `stadium_translations`.`locale` = 'en' and `country_translations`.`locale` = 'en' and `club_translations`.`locale` = 'en' and (`clubs`.`deleted_at` is null)", "type": "query", "params": [], "bindings": ["en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.561192, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "ticketgol", "explain": null, "start_percent": 9.646, "width_percent": 2.631}, {"sql": "select `clubs`.*, `club_translations`.`name` as `club_name`, `club_translations`.`description` as `club_description`, `stadium_translations`.`name` as `stadium_name`, `country_translations`.`name` as `country_name` from `clubs` left join `club_translations` on `clubs`.`id` = `club_translations`.`club_id` left join `stadium_translations` on `clubs`.`stadium_id` = `stadium_translations`.`stadium_id` left join `country_translations` on `clubs`.`country_id` = `country_translations`.`country_id` where `stadium_translations`.`locale` = 'en' and `country_translations`.`locale` = 'en' and `club_translations`.`locale` = 'en' and (`clubs`.`deleted_at` is null) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["en", "en", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.563798, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "ticketgol", "explain": null, "start_percent": 12.277, "width_percent": 2.97}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-02 10:15:28' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.499937, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 15.248, "width_percent": 1.924}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-02 10:15:28' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.505793, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 17.171, "width_percent": 1.782}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-02 10:15:28' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.511889, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 18.953, "width_percent": 2.207}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-02 10:15:28' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.517967, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 21.16, "width_percent": 2.15}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-02 10:15:28' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.524513, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 23.31, "width_percent": 2.405}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-02 10:15:28' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.53155, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 25.714, "width_percent": 2.008}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-02 10:15:28' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.537144, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 27.723, "width_percent": 2.093}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-02 10:15:28' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.543418, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 29.816, "width_percent": 2.037}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-02 10:15:28' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.549762, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 31.853, "width_percent": 1.952}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-02 10:15:28' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.555214, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 33.805, "width_percent": 1.98}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.5630238, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 35.785, "width_percent": 1.726}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-02 10:15:28' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.569328, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 37.511, "width_percent": 2.008}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-02 10:15:28' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.576836, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 39.519, "width_percent": 1.924}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-02 10:15:28' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.584057, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 41.443, "width_percent": 1.81}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-02 10:15:28' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.591665, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 43.253, "width_percent": 2.15}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-02 10:15:28' and `user_type` = 'admin' and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.635056, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "AdminUserResource.php:190", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/AdminUserResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\AdminUserResource.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FAdminUserResource.php&line=190", "ajax": false, "filename": "AdminUserResource.php", "line": "190"}, "connection": "ticketgol", "explain": null, "start_percent": 45.403, "width_percent": 2.433}, {"sql": "select count(*) as aggregate from `clubs` where `created_at` >= '2025-06-02 10:15:28' and `clubs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.642304, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 47.836, "width_percent": 4.356}, {"sql": "select count(*) as aggregate from `cms_pages` where `created_at` >= '2025-06-02 10:15:28' and `cms_pages`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.649539, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 52.192, "width_percent": 2.065}, {"sql": "select count(*) as aggregate from `countries` where `created_at` >= '2025-06-02 10:15:28' and `countries`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.656174, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 54.257, "width_percent": 2.716}, {"sql": "select count(*) as aggregate from `users` where `created_at` >= '2025-06-02 10:15:28' and `user_type` in ('broker', 'customer') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28", "broker", "customer"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.662444, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CustomerResource.php:234", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/CustomerResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\CustomerResource.php", "line": 234}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FCustomerResource.php&line=234", "ajax": false, "filename": "CustomerResource.php", "line": "234"}, "connection": "ticketgol", "explain": null, "start_percent": 56.973, "width_percent": 1.895}, {"sql": "select count(*) as aggregate from `email_templates` where `created_at` >= '2025-06-02 10:15:28' and `email_templates`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.6680999, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 58.868, "width_percent": 2.093}, {"sql": "select count(*) as aggregate from `events` where `created_at` >= '2025-06-02 10:15:28' and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.675253, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 60.962, "width_percent": 2.037}, {"sql": "select count(*) as aggregate from `leagues` where `created_at` >= '2025-06-02 10:15:28' and `leagues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.682364, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 62.999, "width_percent": 2.037}, {"sql": "select count(*) as aggregate from `orders` where `created_at` >= '2025-06-02 10:15:28' and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.68961, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 65.035, "width_percent": 1.895}, {"sql": "select count(*) as aggregate from `restrictions` where `created_at` >= '2025-06-02 10:15:28' and `restrictions`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.696413, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 66.931, "width_percent": 2.122}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.703901, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "RoleResource.php:196", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/RoleResource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Filament\\Resources\\RoleResource.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FFilament%2FResources%2FRoleResource.php&line=196", "ajax": false, "filename": "RoleResource.php", "line": "196"}, "connection": "ticketgol", "explain": null, "start_percent": 69.052, "width_percent": 1.584}, {"sql": "select count(*) as aggregate from `seasons` where `created_at` >= '2025-06-02 10:15:28' and `seasons`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.709491, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 70.636, "width_percent": 1.754}, {"sql": "select count(*) as aggregate from `stadiums` where `created_at` >= '2025-06-02 10:15:28' and `stadiums`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.714227, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 72.39, "width_percent": 2.093}, {"sql": "select count(*) as aggregate from `support_requests` where `created_at` >= '2025-06-02 10:15:28' and `support_requests`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.720607, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 74.484, "width_percent": 2.065}, {"sql": "select count(*) as aggregate from `tickets` where `created_at` >= '2025-06-02 10:15:28' and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-06-02 10:15:28"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 138}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1748945728.72725, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "NavigationBadgeTrait.php:11", "source": {"index": 16, "namespace": null, "name": "app/Traits/NavigationBadgeTrait.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Traits\\NavigationBadgeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FTraits%2FNavigationBadgeTrait.php&line=11", "ajax": false, "filename": "NavigationBadgeTrait.php", "line": "11"}, "connection": "ticketgol", "explain": null, "start_percent": 76.549, "width_percent": 1.952}, {"sql": "update `sessions` set `payload` = 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiUlNWTlJhQUxaMmt4ZVMycE5nWjJxbUNDZTIwM3R2OWtYclI2N2dlSCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMzOiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvY2x1YnMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkcjRzcEIyOW9qMWMyZW53WTE1czgvZWsuS3FrU0tycmI1UE16Z0tKM0lKVWhDZW9CY3NQRzYiO30=', `last_activity` = 1748945728, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'UUSnUxEMdMwVKgxj28zxHDJH8p3IjljVcNv0YgJp'", "type": "query", "params": [], "bindings": ["YTo2OntzOjY6Il90b2tlbiI7czo0MDoiUlNWTlJhQUxaMmt4ZVMycE5nWjJxbUNDZTIwM3R2OWtYclI2N2dlSCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMzOiJodHRwOi8vYWRtaW4udGlja2V0Z29sLnRlc3QvY2x1YnMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkcjRzcEIyOW9qMWMyZW53WTE1czgvZWsuS3FrU0tycmI1UE16Z0tKM0lKVWhDZW9CY3NQRzYiO30=", 1748945728, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "UUSnUxEMdMwVKgxj28zxHDJH8p3IjljVcNv0YgJp"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": 1748945728.819789, "duration": 0.0076, "duration_str": "7.6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 78.501, "width_percent": 21.499}]}, "models": {"data": {"App\\Models\\Club": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FClub.php&line=1", "ajax": false, "filename": "Club.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 12, "is_counter": true}, "livewire": {"data": {"app.filament.resources.club-resource.pages.manage-clubs #BbbsjliC37LLugBM6xTX": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"trashed\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:3 [\n      \"created_at\" => false\n      \"updated_at\" => false\n      \"deleted_at\" => false\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.club-resource.pages.manage-clubs\"\n  \"component\" => \"App\\Filament\\Resources\\ClubResource\\Pages\\ManageClubs\"\n  \"id\" => \"BbbsjliC37LLugBM6xTX\"\n]", "filament-language-switch #JrifG86wZk7CHDBkwDAI": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament-language-switch\"\n  \"component\" => \"BezhanSalleh\\FilamentLanguageSwitch\\Http\\Livewire\\FilamentLanguageSwitch\"\n  \"id\" => \"JrifG86wZk7CHDBkwDAI\"\n]", "filament.livewire.notifications #Lwsok0ey1izS3DVuHhMP": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#7721\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"Lwsok0ey1izS3DVuHhMP\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 196, "messages": [{"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-798058454 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798058454\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.520572, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1237994074 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237994074\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.521552, "xdebug_link": null}, {"message": "[\n  ability => create_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-131569628 data-indent-pad=\"  \"><span class=sf-dump-note>create_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131569628\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.525289, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1103752305 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103752305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.526214, "xdebug_link": null}, {"message": "[\n  ability => reorder_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1546660488 data-indent-pad=\"  \"><span class=sf-dump-note>reorder_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">reorder_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546660488\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.53022, "xdebug_link": null}, {"message": "[\n  ability => reorder,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-573804833 data-indent-pad=\"  \"><span class=sf-dump-note>reorder App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">reorder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573804833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.531, "xdebug_link": null}, {"message": "[\n  ability => delete_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-972137407 data-indent-pad=\"  \"><span class=sf-dump-note>delete_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">delete_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-972137407\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539087, "xdebug_link": null}, {"message": "[\n  ability => deleteAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1460886661 data-indent-pad=\"  \"><span class=sf-dump-note>deleteAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">deleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460886661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539971, "xdebug_link": null}, {"message": "[\n  ability => force_delete_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1492348279 data-indent-pad=\"  \"><span class=sf-dump-note>force_delete_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">force_delete_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492348279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541807, "xdebug_link": null}, {"message": "[\n  ability => forceDeleteAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-608801889 data-indent-pad=\"  \"><span class=sf-dump-note>forceDeleteAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">forceDeleteAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-608801889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.542466, "xdebug_link": null}, {"message": "[\n  ability => restore_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-63595109 data-indent-pad=\"  \"><span class=sf-dump-note>restore_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">restore_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63595109\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.544616, "xdebug_link": null}, {"message": "[\n  ability => restoreAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1899403076 data-indent-pad=\"  \"><span class=sf-dump-note>restoreAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">restoreAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1899403076\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.545532, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-914087513 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914087513\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.692681, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2023511963 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023511963\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.693856, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1706120882 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706120882\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.700233, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-612826607 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612826607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.701083, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1437608684 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437608684\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.73598, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1014401040 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014401040\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.737284, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1913038768 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913038768\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.755571, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1828115704 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828115704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.760778, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2119719506 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119719506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.78303, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2041046534 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041046534\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.785706, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1953035108 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953035108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.80941, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-662730951 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-662730951\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.810365, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1976948071 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976948071\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.824598, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1933938048 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933938048\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.826958, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1174153115 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174153115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.831926, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2047304896 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047304896\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.833054, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-730209406 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730209406\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885353, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1395988182 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395988182\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.88648, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1386569878 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386569878\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.895229, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1652850017 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1652850017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.900268, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1908262049 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908262049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.918094, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-516252108 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-516252108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.919795, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1912212585 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1912212585\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.949532, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-329291477 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-329291477\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.951, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1542457622 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542457622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.966676, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-130874965 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130874965\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.968377, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1551451966 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551451966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.97508, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-930278304 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930278304\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.97603, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1075558976 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075558976\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.998853, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1812893639 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812893639\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.999747, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-203098220 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203098220\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.003629, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1162589491 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162589491\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.004532, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-133567827 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-133567827\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.012506, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-5530193 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5530193\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.013579, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1144871764 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144871764\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.028249, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=3),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1093617133 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=3)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=3)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093617133\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.029317, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1689752760 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689752760\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.04376, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-904659167 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904659167\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.044864, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-745604120 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745604120\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.050242, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-681563214 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681563214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.051596, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1084695430 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084695430\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.082096, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-407770781 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407770781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.082969, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-743014541 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743014541\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.086349, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-329295727 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-329295727\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.087206, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-547117992 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547117992\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.092661, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-982100187 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-982100187\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.093474, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1304963659 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304963659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.102819, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=4),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-352851762 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=4)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=4)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-352851762\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.103751, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1464988206 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464988206\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.112772, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1027395772 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027395772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.113806, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2078055045 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078055045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.117148, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2131298259 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131298259\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.118057, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-69745820 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-69745820\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.137995, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-787923514 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787923514\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.138824, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1324882913 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324882913\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.142193, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1367386112 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367386112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.142953, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-88157059 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88157059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.149474, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-356115501 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356115501\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.150303, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-385301659 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385301659\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.159602, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=5),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1295712738 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=5)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=5)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295712738\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.16044, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1176209198 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176209198\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.170644, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1848632289 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848632289\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.171682, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2016699891 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016699891\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.176111, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-868867166 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868867166\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.176889, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-190676615 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190676615\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.199464, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1395745621 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395745621\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.200316, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-117592761 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117592761\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.204452, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-457504675 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-457504675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.205211, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-979221862 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-979221862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.211056, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1048400422 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048400422\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.212061, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-724727706 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724727706\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.22089, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=6),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-272431125 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=6)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=6)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-272431125\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.222106, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-259249894 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-259249894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.233181, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-418101210 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-418101210\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.234041, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1579799188 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579799188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.237977, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1910458011 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910458011\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.23869, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2044212857 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044212857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.259387, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-737152523 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737152523\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.260143, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1227538986 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1227538986\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.263612, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1952639530 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952639530\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.264192, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1394228123 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394228123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.269498, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1231531010 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231531010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.270218, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-708076207 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708076207\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.278785, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=7),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-673266158 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=7)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=7)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673266158\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.279259, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1088116648 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088116648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.287855, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-679467539 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679467539\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.288782, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1346291891 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346291891\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.292691, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-261034797 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261034797\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.293392, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-627604538 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627604538\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.314399, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-995505263 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995505263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.315505, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-347207959 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347207959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.319478, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1754498608 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754498608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.320395, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1467610929 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467610929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.327279, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-45102887 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45102887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.328237, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-75549451 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75549451\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.337566, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=8),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-296612117 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=8)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=8)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-296612117\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.338521, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-175100323 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-175100323\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.349511, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-293012134 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-293012134\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.350454, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1623602460 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623602460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.354536, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1288869504 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288869504\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.355323, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1054028158 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054028158\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.374546, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1804273216 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804273216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.375486, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-558002272 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558002272\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.379652, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-15555425 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15555425\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.380327, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1234815989 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234815989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.386518, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1582999680 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582999680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.387299, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-677436573 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677436573\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.396552, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=9),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1926698547 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=9)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\Club(id=9)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926698547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.397402, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1076989693 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076989693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.40557, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1601408553 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Club(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601408553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.406135, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2037621282 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037621282\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.410418, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-580782790 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Club(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580782790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.411087, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1183024509 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183024509\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.428811, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-961840438 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Club(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961840438\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.429666, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-316317592 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-316317592\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.433699, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1298907552 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Club(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298907552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.434606, "xdebug_link": null}, {"message": "[\n  ability => update_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-977806825 data-indent-pad=\"  \"><span class=sf-dump-note>update_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">update_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977806825\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.440062, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\Club(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-452665728 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\Club(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Club(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452665728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.44129, "xdebug_link": null}, {"message": "[\n  ability => delete_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1235785008 data-indent-pad=\"  \"><span class=sf-dump-note>delete_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235785008\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.450084, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\Club(id=10),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\Club)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-979411111 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\Club(id=10)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Club(id=10)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[0 =&gt; Object(App\\Models\\Club)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-979411111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.450976, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-205791916 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205791916\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.495936, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-902835173 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902835173\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.498116, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1598486222 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1598486222\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.503228, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1638156761 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1638156761\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.504116, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-256015742 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256015742\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.509242, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1012390026 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1012390026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.510014, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1689277668 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689277668\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.515824, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-994303782 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994303782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.516481, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1037068435 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1037068435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.521714, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1191850840 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191850840\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.522623, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-72036001 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72036001\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.529004, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1237586366 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237586366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.529762, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1091947831 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091947831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.534707, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-541797490 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541797490\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.535447, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-157936146 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-157936146\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.540825, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-429589297 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429589297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.541746, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1464391213 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464391213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.547423, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1361923754 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1361923754\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.548156, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-584066313 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-584066313\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.552856, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1680577952 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680577952\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.553494, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-369745453 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369745453\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.559641, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-432177637 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432177637\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.560579, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1243931745 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243931745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.566924, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1681937871 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681937871\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.567634, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-919365266 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919365266\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.574346, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1079726710 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1079726710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.574994, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1861698358 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861698358\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.581185, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-236842542 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236842542\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.582218, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1716036299 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716036299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.588683, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1235059323 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235059323\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.589602, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1496881744 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1496881744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.595181, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1985106804 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985106804\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.596038, "xdebug_link": null}, {"message": "[\n  ability => page_GeneralSettings,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-485109994 data-indent-pad=\"  \"><span class=sf-dump-note>page_GeneralSettings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">page_GeneralSettings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485109994\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.630934, "xdebug_link": null}, {"message": "[\n  ability => view_any_admin::user,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-113434615 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_admin::user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_admin::user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113434615\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.632968, "xdebug_link": null}, {"message": "[\n  ability => view_any_club,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-658998467 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_club </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_club</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-658998467\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.639534, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Club,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Club]\n]", "message_html": "<pre class=sf-dump id=sf-dump-799641185 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Club</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Club</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Club]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799641185\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.640394, "xdebug_link": null}, {"message": "[\n  ability => view_any_cms::page,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-178117188 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_cms::page </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">view_any_cms::page</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-178117188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.646485, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\CmsPage,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\CmsPage]\n]", "message_html": "<pre class=sf-dump id=sf-dump-454047055 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\CmsPage</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\CmsPage</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\CmsPage]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-454047055\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.647347, "xdebug_link": null}, {"message": "[\n  ability => view_any_country,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-46929385 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_country </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_country</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46929385\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.653246, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Country,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Country]\n]", "message_html": "<pre class=sf-dump id=sf-dump-703291908 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Country</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Country</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Country]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703291908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.654099, "xdebug_link": null}, {"message": "[\n  ability => view_any_customer,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2028025222 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028025222\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.659865, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-626290499 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-626290499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.660605, "xdebug_link": null}, {"message": "[\n  ability => view_any_email::template,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1195816171 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_email::template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">view_any_email::template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195816171\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.665895, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\EmailTemplate,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\EmailTemplate]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1757617853 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\EmailTemplate</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"24 characters\">App\\Models\\EmailTemplate</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"31 characters\">[0 =&gt; App\\Models\\EmailTemplate]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757617853\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.666611, "xdebug_link": null}, {"message": "[\n  ability => view_any_event,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1584704944 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_event </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_event</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584704944\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.671975, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Event,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Event]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1253972817 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Event</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Event</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Event]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253972817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.672949, "xdebug_link": null}, {"message": "[\n  ability => view_any_league,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-701479166 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_league </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_league</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701479166\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.679642, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\League,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\League]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1575103443 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\League</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\League</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\League]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575103443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.680543, "xdebug_link": null}, {"message": "[\n  ability => view_any_order,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-674084921 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_any_order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674084921\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.687021, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Order,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Order]\n]", "message_html": "<pre class=sf-dump id=sf-dump-603938954 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"16 characters\">App\\Models\\Order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[0 =&gt; App\\Models\\Order]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-603938954\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.688042, "xdebug_link": null}, {"message": "[\n  ability => view_any_restriction,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-47829658 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_restriction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">view_any_restriction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47829658\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.693788, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Restriction,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Restriction]\n]", "message_html": "<pre class=sf-dump id=sf-dump-524102229 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Restriction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"22 characters\">App\\Models\\Restriction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"29 characters\">[0 =&gt; App\\Models\\Restriction]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524102229\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.694661, "xdebug_link": null}, {"message": "[\n  ability => view_any_role,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1394002381 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_role </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_any_role</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394002381\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.701031, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-44751399 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44751399\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.702269, "xdebug_link": null}, {"message": "[\n  ability => view_any_season,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1970910525 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_season </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_season</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970910525\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.70732, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Season,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Season]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1603087862 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Season</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Season</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Season]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603087862\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.708094, "xdebug_link": null}, {"message": "[\n  ability => view_any_stadium,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1958055248 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_stadium </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_any_stadium</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958055248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.712149, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Stadium,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Stadium]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1255795149 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Stadium</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Stadium</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Stadium]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255795149\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.712797, "xdebug_link": null}, {"message": "[\n  ability => view_any_support::request,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-553580138 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_support::request </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">view_any_support::request</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553580138\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.718241, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\SupportRequest,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\SupportRequest]\n]", "message_html": "<pre class=sf-dump id=sf-dump-200767006 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\SupportRequest</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"25 characters\">App\\Models\\SupportRequest</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"32 characters\">[0 =&gt; App\\Models\\SupportRequest]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-200767006\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.718958, "xdebug_link": null}, {"message": "[\n  ability => view_any_ticket,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-930912828 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_ticket </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">view_any_ticket</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930912828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.724962, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Ticket,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Ticket]\n]", "message_html": "<pre class=sf-dump id=sf-dump-201405374 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Ticket</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Ticket</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Ticket]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201405374\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.725727, "xdebug_link": null}, {"message": "[\n  ability => view_any_activity,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2052176483 data-indent-pad=\"  \"><span class=sf-dump-note>view_any_activity </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">view_any_activity</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2052176483\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.73016, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => Spatie\\Activitylog\\Models\\Activity,\n  result => true,\n  user => 1,\n  arguments => [0 => Spatie\\Activitylog\\Models\\Activity]\n]", "message_html": "<pre class=sf-dump id=sf-dump-170572590 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Activitylog\\Models\\Activity</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Spatie\\Activitylog\\Models\\Activity</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Spatie\\Activitylog\\Models\\Activity]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-170572590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1748945728.73072, "xdebug_link": null}]}, "session": {"_token": "RSVNRaALZ2kxeS2pNgZ2qmCCe203tv9kXrR67geH", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://admin.ticketgol.test/clubs\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$12$r4spB29oj1c2enwY15s8/ek.KqkSKrrb5PMzgKJ3IJUhCeoBcsPG6"}, "request": {"telescope": "<a href=\"http://admin.ticketgol.test/_debugbar/telescope/9f10d0e3-ce60-410d-883b-9edec1e94ea2\" target=\"_blank\">View in Telescope</a>", "path_info": "/clubs", "status_code": "<pre class=sf-dump id=sf-dump-1884940262 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1884940262\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2089196880 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2089196880\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1099831321 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1099831321\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1483697063 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1090 characters\">selected_locale=en; filament_language_switch_locale=eyJpdiI6IlFoT3lkSS9LbGtzcUhWWGNYNzZjNVE9PSIsInZhbHVlIjoiN3B5YjBONGFhdmJaNVNSNlpPd0UydXhhdUw4RVdxam5WckYzSytWdXdlVkZaVjhGUGJtUWZicXZER0tvdVVTdiIsIm1hYyI6ImNmYjUyMWUyZTJlNTYxMjllYmU0NjJkMzgzZWFlOTRhZjViMTBmZmE3YWI4ZmQ0MjAyNmM5YzQ3OGY5MzcyN2YiLCJ0YWciOiIifQ%3D%3D; ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; XSRF-TOKEN=eyJpdiI6IlpleWRBTVFsWGYwVzZGbnpFQWdPUFE9PSIsInZhbHVlIjoiYll0a1ZqM2ZRS1ZHZkloU250cWdYMldQU0F3RS9Qd0Q4NmJJanFVZDVkdUNyRXRmNUNxYlU2UDRQV1BkeFBKYjVMRDlPczd2UVFtYVV2QlhyRVdUMmh1cE1KYTYvaG5ubHovS28zMGlvSHg0YmFSdU1iQmh4VnJ2aHVGRjZ0TDkiLCJtYWMiOiI2YjAwMzRiNWNmZTNkMjFjZDVjNGM1YTQxM2IzM2ZjODA2MTUwMTFmMmNjNjU1ODgyYzNlN2IyMjllMDE5OGY2IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6IkcrQ2ZzTUpaTXdFckhYVmFNWFJCQUE9PSIsInZhbHVlIjoiQURMWVNYR0hEdSttRDZkbzhXcDlzZ1JrL2h3YmszWit0ejJIR2JKU2JNZzJ0OGxMVXdmNWN6aVVxMHRRa0E0amh5Y1pVcVdoaWhyQzF6MEJRWCs3cGNUNk5YeUlBSmh0RzNONmZjNmdRNW54UVVKYVl2UkhjaGc0OVFmL0tDcy8iLCJtYWMiOiJiZTU5NDQxMTllNmE0MTMzZWQ0MzAyZWU0OTY2ZThhNDM0ODk0MGQ1MGM0ZjBhYmNlMmQ1ZGZiYmEwYTBhNzI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://admin.ticketgol.test/clubs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483697063\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-582247001 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>filament_language_switch_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RSVNRaALZ2kxeS2pNgZ2qmCCe203tv9kXrR67geH</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UUSnUxEMdMwVKgxj28zxHDJH8p3IjljVcNv0YgJp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582247001\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-813015931 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 10:15:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imo3bkxMVzRLN29WOER6U0lqOXJXTUE9PSIsInZhbHVlIjoiRjhyVDJWTDBzeDQwdWR6SityWHJVZ2ZlVDl4aitITTlFWTJ6eVJXU1pXWExtQXJySCtBRmtHd29XTlZ2Q3ZLUDlvQ0o0UFJqbFA5aVYxN2JaV2JiQkFZOG1ubndDUWJVRFZrZHI2QStTY2pFdFZIVXNiRVo0OHJJOFdrcGhHQXciLCJtYWMiOiJkMGQ1MGQwMWU0MDU1MGIxZjI1NzA5ZjU5MmRlY2E0MWMxNDZmZDhkNDBmM2MwODg1YzE2ZTUyNzEyNzI2YTE0IiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 12:15:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6ImRJMnBqSUtDN08reER5YnFMYlVjd3c9PSIsInZhbHVlIjoiNTJBRVRXbmdzaUlOWDBRU2V0T2tUNlI2OFpsR3pTRTNVUG9XV29FTlVRWXVVRW5tSGhIb3kzZFBpb01RNnFaU3hMaWZrdk8rQUpNMVpMYk9FRjlXQ3dRMElmVzJXbE5HcE5vUzJSZnlMYlZvMnFzelgzZU80L0puczd2Snc4cFAiLCJtYWMiOiI5MjliYzY4OWFhMGFkNDNiODE1YmU5ZTc3N2U4NmFhZTY5YjIwNTFjY2JlYTZkYzk1YTI5OTczNDU1OTdiYzczIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 12:15:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imo3bkxMVzRLN29WOER6U0lqOXJXTUE9PSIsInZhbHVlIjoiRjhyVDJWTDBzeDQwdWR6SityWHJVZ2ZlVDl4aitITTlFWTJ6eVJXU1pXWExtQXJySCtBRmtHd29XTlZ2Q3ZLUDlvQ0o0UFJqbFA5aVYxN2JaV2JiQkFZOG1ubndDUWJVRFZrZHI2QStTY2pFdFZIVXNiRVo0OHJJOFdrcGhHQXciLCJtYWMiOiJkMGQ1MGQwMWU0MDU1MGIxZjI1NzA5ZjU5MmRlY2E0MWMxNDZmZDhkNDBmM2MwODg1YzE2ZTUyNzEyNzI2YTE0IiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 12:15:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6ImRJMnBqSUtDN08reER5YnFMYlVjd3c9PSIsInZhbHVlIjoiNTJBRVRXbmdzaUlOWDBRU2V0T2tUNlI2OFpsR3pTRTNVUG9XV29FTlVRWXVVRW5tSGhIb3kzZFBpb01RNnFaU3hMaWZrdk8rQUpNMVpMYk9FRjlXQ3dRMElmVzJXbE5HcE5vUzJSZnlMYlZvMnFzelgzZU80L0puczd2Snc4cFAiLCJtYWMiOiI5MjliYzY4OWFhMGFkNDNiODE1YmU5ZTc3N2U4NmFhZTY5YjIwNTFjY2JlYTZkYzk1YTI5OTczNDU1OTdiYzczIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 12:15:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813015931\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-57129384 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RSVNRaALZ2kxeS2pNgZ2qmCCe203tv9kXrR67geH</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://admin.ticketgol.test/clubs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$r4spB29oj1c2enwY15s8/ek.KqkSKrrb5PMzgKJ3IJUhCeoBcsPG6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57129384\", {\"maxDepth\":0})</script>\n"}}