<?php

namespace App\Services;

use App\DTO\OrderFilterDTO;
use App\Http\Resources\OrderResource;
use App\Repositories\OrderRepository;
use App\Repositories\OrderTransactionRepository;

class OrderService
{
    private $orderRepository;

    private $orderTransactionRepository;

    public function __construct()
    {
        $this->orderRepository = app(OrderRepository::class);
        $this->orderTransactionRepository = app(OrderTransactionRepository::class);
    }

    public function getPaginatedOrdersForUser($userId, $perPage = 10, OrderFilterDTO $filtersDTO = null, $page = 1)
    {
        // If no DTO is provided, create an empty one
        if (!$filtersDTO) {
            $filtersDTO = new OrderFilterDTO();
        }

        $orders = $this->orderRepository->getOrdersForUser($userId, $perPage, $filtersDTO->toArray(), $page);

        return OrderResource::collection($orders);
    }
}
