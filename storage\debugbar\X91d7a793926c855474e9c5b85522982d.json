{"__meta": {"id": "X91d7a793926c855474e9c5b85522982d", "datetime": "2025-06-03 13:36:50", "utime": **********.011995, "method": "GET", "uri": "/api/v1/orders?search=&status=&date_from=&date_to=&page=1", "ip": "127.0.0.1"}, "php": {"version": "8.4.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.846155, "end": **********.012013, "duration": 0.16585803031921387, "duration_str": "166ms", "measures": [{"label": "Booting", "start": **********.846155, "relative_start": 0, "end": **********.946088, "relative_end": **********.946088, "duration": 0.09993314743041992, "duration_str": "99.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.946108, "relative_start": 0.09995317459106445, "end": **********.012016, "relative_end": 3.0994415283203125e-06, "duration": 0.06590795516967773, "duration_str": "65.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5874008, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/orders", "middleware": "api, set-locale, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\V1\\OrderController@index", "namespace": null, "where": [], "as": "api.orders.index", "prefix": "api/v1/orders", "file": "<a href=\"phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FHttp%2FControllers%2FApi%2FV1%2FOrderController.php&line=19\" onclick=\"\">app/Http/Controllers/Api/V1/OrderController.php:19-30</a>"}, "queries": {"nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01638, "accumulated_duration_str": "16.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'fKBgzTfzGip9Luoo5z6UwW4m855UzBBWaIyp60rB' limit 1", "type": "query", "params": [], "bindings": ["fKBgzTfzGip9Luoo5z6UwW4m855UzBBWaIyp60rB"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.953369, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "ticketgol", "explain": null, "start_percent": 0, "width_percent": 3.724}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "auth.session", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.958676, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ticketgol", "explain": null, "start_percent": 3.724, "width_percent": 5.433}, {"sql": "select count(*) as aggregate from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 17, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 29}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 27}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.966536, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 9.158, "width_percent": 5.25}, {"sql": "select `id`, `order_no`, `buyer_id`, `ticket_id`, `quantity`, `total_price`, `status`, `purchase_date`, `created_at` from `orders` where `buyer_id` = 7 and `orders`.`deleted_at` is null order by `created_at` desc limit 9 offset 0", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 17, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 29}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 27}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.969105, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 16, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 14.408, "width_percent": 4.518}, {"sql": "select `id`, `ticket_no`, `event_id`, `sector_id` from `tickets` where `tickets`.`id` in (4, 12) and `tickets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 29}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.9722588, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 18.926, "width_percent": 3.663}, {"sql": "select `id`, `slug` from `events` where `events`.`id` in (8) and `events`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 27, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 29}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 27}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.976336, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 22.589, "width_percent": 3.907}, {"sql": "select `id`, `event_id`, `name` from `event_translations` where `locale` = 'en' and `event_translations`.`event_id` in (8)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 29}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 27}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.9806042, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 26.496, "width_percent": 4.029}, {"sql": "select `id` from `stadium_sectors` where `stadium_sectors`.`id` in (138) and `stadium_sectors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 27, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 29}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 27}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 30, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.983697, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 26, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 30.525, "width_percent": 2.991}, {"sql": "select `id`, `stadium_sector_id`, `name` from `stadium_sector_translations` where `locale` = 'en' and `stadium_sector_translations`.`stadium_sector_id` in (138)", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 32, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 29}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 27}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 35, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.986853, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 31, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 33.516, "width_percent": 3.724}, {"sql": "select `id`, `order_id`, `name`, `email`, `gender`, `dob` from `attendees` where `attendees`.`order_id` in (15, 16, 17)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 29}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.9910688, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 37.241, "width_percent": 6.777}, {"sql": "select `id`, `order_id`, `status`, `currency_code`, `total_amount`, `paid_at`, `created_at` from `order_transactions` where `order_transactions`.`order_id` in (15, 16, 17)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, {"index": 22, "namespace": null, "name": "app/Services/OrderService.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Services\\OrderService.php", "line": 29}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Api/V1/OrderController.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Http\\Controllers\\Api\\V1\\OrderController.php", "line": 27}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/sentry/sentry-laravel/src/Sentry/Laravel/Tracing/Routing/TracingControllerDispatcherTracing.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php", "line": 21}], "start": **********.995018, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "OrderRepository.php:110", "source": {"index": 21, "namespace": null, "name": "app/Repositories/OrderRepository.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\app\\Repositories\\OrderRepository.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FRepositories%2FOrderRepository.php&line=110", "ajax": false, "filename": "OrderRepository.php", "line": "110"}, "connection": "ticketgol", "explain": null, "start_percent": 44.017, "width_percent": 4.64}, {"sql": "update `sessions` set `payload` = 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiaDY2eUdXNEdqdkplQXFQT3BWSXpXNDJJQThac0ZsYlRVM0Y3c0VWayI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czozOToiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzIjt9czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==', `last_activity` = **********, `user_id` = 7, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'fKBgzTfzGip9Luoo5z6UwW4m855UzBBWaIyp60rB'", "type": "query", "params": [], "bindings": ["YTo1OntzOjY6Il90b2tlbiI7czo0MDoiaDY2eUdXNEdqdkplQXFQT3BWSXpXNDJJQThac0ZsYlRVM0Y3c0VWayI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NztzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czozOToiaHR0cDovL3RpY2tldGdvbC50ZXN0L215LWFjY291bnQvb3JkZXJzIjt9czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRNemY3bzRoMWZramJiSU44VUxIOVoudG1VWVNDVjhuUVlKZG9uVG9zNy9FUzJvV0QxZ3ZvNiI7fQ==", **********, 7, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "fKBgzTfzGip9Luoo5z6UwW4m855UzBBWaIyp60rB"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.002284, "duration": 0.00841, "duration_str": "8.41ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\Ticketgol\\Code\\Ticketgol\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "ticketgol", "explain": null, "start_percent": 48.657, "width_percent": 51.343}]}, "models": {"data": {"App\\Models\\Order": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "App\\Models\\Attendee": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FAttendee.php&line=1", "ajax": false, "filename": "Attendee.php", "line": "?"}}, "App\\Models\\OrderTransaction": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FOrderTransaction.php&line=1", "ajax": false, "filename": "OrderTransaction.php", "line": "?"}}, "App\\Models\\Ticket": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FTicket.php&line=1", "ajax": false, "filename": "Ticket.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Event": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "App\\Models\\EventTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FEventTranslation.php&line=1", "ajax": false, "filename": "EventTranslation.php", "line": "?"}}, "App\\Models\\StadiumSector": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSector.php&line=1", "ajax": false, "filename": "StadiumSector.php", "line": "?"}}, "App\\Models\\StadiumSectorTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FTicketgol%2FCode%2FTicketgol%2Fapp%2FModels%2FStadiumSectorTranslation.php&line=1", "ajax": false, "filename": "StadiumSectorTranslation.php", "line": "?"}}}, "count": 16, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "h66yGW4GjvJeAqPOpVIzW42IA8ZsFlbTU3F7sEVk", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "_previous": "array:1 [\n  \"url\" => \"http://ticketgol.test/my-account/orders\"\n]", "password_hash_web": "$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6"}, "request": {"telescope": "<a href=\"http://ticketgol.test/_debugbar/telescope/9f1118e6-3918-4951-9b80-7a8f3e55b350\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/orders", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_to</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1525597423 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1525597423\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-935877693 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"796 characters\">ajs_anonymous_id=%2205cf8d6b-8a14-40b9-9d4f-c4ebd1620a6e%22; selected_locale=en; XSRF-TOKEN=eyJpdiI6IkNldkowVkNCeElEZG05MDJvbDZsSWc9PSIsInZhbHVlIjoiV293S1NxYmtsSUFHNWlFT3FJeFFCUE9KL1YrTlRvU0xTVzVHNFdWUlRXbVovbmhJRndlTWtmem14dndYRmRNM0xzY2NvZ3IwMzlIVktKMXFzakp5b2g3RmFoMHVqN3I4SWtmQzlZVkxxUUNvdWtKd0psNFcrNHZyT2ZTRGtHZkIiLCJtYWMiOiIzNjFiZTJmNjEwZThiOTgyYTA1YTgxNWEwNzliMTliMmY5YzA4Mzk1ZDU4YmYwODJmM2QwNjliYjk4MTM2ZjI5IiwidGFnIjoiIn0%3D; ticketgol_session=eyJpdiI6ImozMjJkSGhaVHpLSkxOMG05ZW5qaWc9PSIsInZhbHVlIjoiVkZ1cHh2TU9BOHc3WGJSQ0JRY1ZUVHQ0ZmswYlk1L3AzKzk4c3FFSEovZnprNFRmalNZTnpEL0hmNDVDSC9OR09NTkNJOEk1OVpJTWxHOUdpbFhkTjIySnpLS2NmbHE4T2FHTG53czgrbmVjSFdob2FleUVnbnFibmVQNkQzQkYiLCJtYWMiOiJjODAyYTliZjIwYWI5N2NiYmZhMTg0N2I5ODkzODc0ODRkODU0Y2ViN2JhYmQzMmNmNDdjZDI0YTdhYmFlMmZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en;q=0.9,es;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-locale</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkNldkowVkNCeElEZG05MDJvbDZsSWc9PSIsInZhbHVlIjoiV293S1NxYmtsSUFHNWlFT3FJeFFCUE9KL1YrTlRvU0xTVzVHNFdWUlRXbVovbmhJRndlTWtmem14dndYRmRNM0xzY2NvZ3IwMzlIVktKMXFzakp5b2g3RmFoMHVqN3I4SWtmQzlZVkxxUUNvdWtKd0psNFcrNHZyT2ZTRGtHZkIiLCJtYWMiOiIzNjFiZTJmNjEwZThiOTgyYTA1YTgxNWEwNzliMTliMmY5YzA4Mzk1ZDU4YmYwODJmM2QwNjliYjk4MTM2ZjI5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">ticketgol.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935877693\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1968230102 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ajs_anonymous_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>selected_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h66yGW4GjvJeAqPOpVIzW42IA8ZsFlbTU3F7sEVk</span>\"\n  \"<span class=sf-dump-key>ticketgol_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fKBgzTfzGip9Luoo5z6UwW4m855UzBBWaIyp60rB</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968230102\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1509892029 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 13:36:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im1scUNiNTE0RWdiazNDUDVwdjJSNEE9PSIsInZhbHVlIjoiQ2docDQrMVJPSWJnTjRWYUlqVjhJWGp2RE43TWZHMFIxdWZDMi9iZ1oxSXlMZS9kMlFORFpzZkNvY2pHaGx5OXJCZ1hocm45MDhDanNmcHdUWkJxaEJBOHQwN3I4eU83ZWxFMGkzUEkwbkhKUFlybGMxeWN5cFJQZ1Q2NGpTNlkiLCJtYWMiOiJiM2IzNDFkZWViZTNkNDY4ODViNjgxYjY5ZTJlMjBlY2QyMmQzMGI4OWIzM2RlNDljZDE0MWY3MzFiMTc5MmUyIiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 15:36:50 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"445 characters\">ticketgol_session=eyJpdiI6ImtWcFlQQXkrdHlkTVhrNzBNek1iY2c9PSIsInZhbHVlIjoiS3d1T1lLRUNPdTdUTmJBbUh0TWYxa2Z1aHNjV1h6TmNJM0NCaDJOeEtkWjN3Rm5iMy9BTE5YR0tZWEZjRXNnOWhraUJxUmhDejM4L3lrS0VmV01pVkNDbGJLelF3Z01sUHlMdFFpaUwrZEVxZ2JWR01pQS84TkducThZQlArQnQiLCJtYWMiOiI1OTM4YmYyMjM1YzI5OTZlODNmNTRlNWI4OWFiNTY5MGU3ZmE3YWQyZTBkMjYyYjMxMjVkOWI3OTY0ZjQyMzg0IiwidGFnIjoiIn0%3D; expires=Tue, 03 Jun 2025 15:36:50 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im1scUNiNTE0RWdiazNDUDVwdjJSNEE9PSIsInZhbHVlIjoiQ2docDQrMVJPSWJnTjRWYUlqVjhJWGp2RE43TWZHMFIxdWZDMi9iZ1oxSXlMZS9kMlFORFpzZkNvY2pHaGx5OXJCZ1hocm45MDhDanNmcHdUWkJxaEJBOHQwN3I4eU83ZWxFMGkzUEkwbkhKUFlybGMxeWN5cFJQZ1Q2NGpTNlkiLCJtYWMiOiJiM2IzNDFkZWViZTNkNDY4ODViNjgxYjY5ZTJlMjBlY2QyMmQzMGI4OWIzM2RlNDljZDE0MWY3MzFiMTc5MmUyIiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 15:36:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"417 characters\">ticketgol_session=eyJpdiI6ImtWcFlQQXkrdHlkTVhrNzBNek1iY2c9PSIsInZhbHVlIjoiS3d1T1lLRUNPdTdUTmJBbUh0TWYxa2Z1aHNjV1h6TmNJM0NCaDJOeEtkWjN3Rm5iMy9BTE5YR0tZWEZjRXNnOWhraUJxUmhDejM4L3lrS0VmV01pVkNDbGJLelF3Z01sUHlMdFFpaUwrZEVxZ2JWR01pQS84TkducThZQlArQnQiLCJtYWMiOiI1OTM4YmYyMjM1YzI5OTZlODNmNTRlNWI4OWFiNTY5MGU3ZmE3YWQyZTBkMjYyYjMxMjVkOWI3OTY0ZjQyMzg0IiwidGFnIjoiIn0%3D; expires=Tue, 03-Jun-2025 15:36:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509892029\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h66yGW4GjvJeAqPOpVIzW42IA8ZsFlbTU3F7sEVk</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://ticketgol.test/my-account/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$Mzf7o4h1fkjbbIN8ULH9Z.tmUYSCV8nQYJdonTos7/ES2oWD1gvo6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}